"""
股票分析RAG系统主入口
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from utils.logger import get_logger
from database.mysql_client import mysql_client
from database.vector_client import vector_client
from llm.model_manager import model_manager

logger = get_logger("main")


async def initialize_system():
    """初始化系统"""
    logger.info("开始初始化股票分析RAG系统...")
    
    try:
        # 初始化数据库连接
        logger.info("初始化数据库连接...")
        mysql_client.initialize()
        vector_client.initialize()
        
        # 初始化模型管理器
        logger.info("初始化模型管理器...")
        model_manager.initialize()
        
        # 系统健康检查
        logger.info("执行系统健康检查...")
        await health_check()
        
        logger.info("系统初始化完成！")
        return True
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False


async def health_check():
    """系统健康检查"""
    checks = []
    
    # MySQL连接检查
    mysql_ok = mysql_client.test_connection()
    checks.append(("MySQL数据库", mysql_ok))
    
    # 向量数据库检查
    try:
        vector_info = vector_client.get_collection_info()
        vector_ok = True
        logger.info(f"向量数据库集合信息: {vector_info}")
    except Exception as e:
        vector_ok = False
        logger.error(f"向量数据库检查失败: {e}")
    checks.append(("ChromaDB向量数据库", vector_ok))
    
    # Ollama服务检查
    model_status = model_manager.get_model_status()
    ollama_ok = model_status["ollama_available"]
    checks.append(("Ollama服务", ollama_ok))
    
    # 输出检查结果
    logger.info("=== 系统健康检查结果 ===")
    for service, status in checks:
        status_text = "✅ 正常" if status else "❌ 异常"
        logger.info(f"{service}: {status_text}")
    
    # 输出可用模型
    available_models = model_status["available_models"]
    logger.info(f"可用模型数量: {len(available_models)}")
    for model in available_models:
        logger.info(f"  - {model}")
    
    # 检查是否所有关键服务都正常
    critical_services = [mysql_ok, vector_ok, ollama_ok]
    if not all(critical_services):
        logger.warning("部分关键服务异常，系统可能无法正常工作")
    else:
        logger.info("所有关键服务正常")


def show_system_info():
    """显示系统信息"""
    logger.info("=== 股票分析RAG系统信息 ===")
    logger.info(f"环境: {settings.environment}")
    logger.info(f"调试模式: {settings.debug}")
    logger.info(f"MySQL数据库: {settings.mysql_host}:{settings.mysql_port}")
    logger.info(f"向量数据库: {settings.chroma_persist_directory}")
    logger.info(f"Ollama服务: {settings.ollama_base_url}")
    logger.info(f"API服务: {settings.api_host}:{settings.api_port}")


async def main():
    """主函数"""
    try:
        # 显示系统信息
        show_system_info()
        
        # 初始化系统
        success = await initialize_system()
        
        if not success:
            logger.error("系统初始化失败，退出程序")
            sys.exit(1)
        
        logger.info("系统启动成功，可以开始使用")
        
        # 这里可以添加其他启动逻辑
        # 比如启动API服务、Web界面等
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭系统...")
    except Exception as e:
        logger.error(f"系统运行异常: {e}")
        sys.exit(1)
    finally:
        # 清理资源
        logger.info("清理系统资源...")
        mysql_client.close()
        vector_client.close()


if __name__ == "__main__":
    asyncio.run(main())
