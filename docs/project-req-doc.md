# 股票分析RAG系统设计文档

## 1. 系统概述

### 1.1 项目目标
构建一个基于外部向量化记忆库和检索增强生成（RAG）的智能股票分析系统，能够：
- 持续学习和记忆历史股票数据
- 提供智能的股票分析和投资建议
- 支持多种数据源的整合分析
- 实现长期记忆和延续性学习

### 1.2 核心特性
- **多模态数据整合**：日线、分钟级行情数据、技术指标、新闻资讯
- **智能检索增强**：基于向量相似度的历史数据检索
- **多模型支持**：GPT、Claude、本地Ollama模型
- **实时数据更新**：自动获取最新市场数据
- **记忆衰减管理**：智能的历史数据权重调整

## 2. 系统架构设计

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                  │
├─────────────────────────────────────────────────────────────┤
│  Web界面  │  API接口  │  命令行工具  │  Jupyter Notebook      │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   查询处理模块    │   分析引擎模块    │    记忆管理模块          │
├─────────────────┼─────────────────┼─────────────────────────┤
│   RAG检索模块    │   LLM调用模块    │    数据预处理模块        │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   向量数据库      │   关系数据库      │    文件存储系统          │
│  (Chroma/FAISS) │  (MySQL)   │    (本地/云存储)         │
├─────────────────────────────────────────────────────────────┤
│                    数据获取层                                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│  Tushare API    │   新闻爬虫       │    技术指标计算          │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### 2.2 核心组件说明

#### 2.2.1 数据获取层
- **现有MySQL数据库**：直接读取已有的股票数据表（无需重复开发）
- **新闻数据爬虫**：从财经网站获取相关新闻（新增功能）
- **数据同步模块**：确保向量数据库与MySQL数据同步

#### 2.2.2 数据存储层
- **向量数据库**：存储向量化的股票数据和新闻信息
- **现有MySQL数据库**：已包含完整的股票数据（stock_cursor库）
- **文件存储**：存储模型文件和临时数据

#### 2.2.3 业务逻辑层
- **RAG检索模块**：基于查询意图检索相关历史数据
- **LLM调用模块**：调用不同的大语言模型进行分析
- **记忆管理模块**：管理历史数据的权重和衰减
- **数据适配模块**：将现有数据库结构适配到RAG系统

## 3. 技术选型

### 3.1 开发语言和框架
- **主要语言**：Python 3.11
- **Web框架**：FastAPI（API服务）+ Streamlit（前端界面）
- **数据处理**：Pandas, NumPy, TA-Lib
- **机器学习**：Scikit-learn, Transformers

### 3.2 数据库选择
- **关系数据库**：MySQL 8.0+（主要数据存储，stock_cursor库）
- **向量数据库**：ChromaDB（轻量级，易部署，本地存储）
- **缓存数据库**：Redis（可选，用于高频访问数据缓存）

### 3.3 大语言模型配置
基于本地Ollama环境的模型选择：

#### 3.3.1 文本生成模型
- **本地大模型**：
  - `qwen2.5:latest` (4.7GB) - 主推荐，中文支持优秀
  - `qwen2.5-coder:latest` (4.7GB) - 代码分析专用
  - `deepseek-r1:latest` (4.7GB) - 推理能力强

#### 3.3.2 向量化嵌入模型
- **本地嵌入模型**：
  - `bge-large:latest` (670MB) - 主推荐，中文向量化效果好
  - `bge-m3:latest` (1.2GB) - 多语言支持，精度更高
  - `nomic-embed-text:latest` (274MB) - 轻量级选择
  - `snowflake-arctic-embed2:latest` (1.2GB) - 高质量英文嵌入

#### 3.3.3 Ollama服务配置
```python
# Ollama配置
OLLAMA_CONFIG = {
    'base_url': 'http://localhost:11434/',
    'default_model': 'qwen2.5:latest',
    'embedding_model': 'bge-large:latest',
    'code_model': 'qwen2.5-coder:latest',
    'reasoning_model': 'deepseek-r1:latest'
}
```

## 4. 现有数据模型说明

### 4.1 现有MySQL数据库表结构
基于您提供的数据库，系统将直接使用以下现有表：

#### 4.1.1 股票基础信息表 (stock_basic)
```sql
-- 已存在，包含股票基本信息
-- ts_code, symbol, name, area, industry, list_date
```

#### 4.1.2 股票日线历史数据表 (stock_daily_history)
```sql
-- 已存在，包含完整的OHLCV数据
-- ts_code, trade_date, open, high, low, close, pre_close, change_c, pct_chg, vol, amount
```

#### 4.1.3 股票技术因子数据表 (stock_factor)
```sql
-- 已存在，包含完整的技术指标
-- MACD, KDJ, RSI, BOLL, CCI等技术指标
-- 前复权和后复权价格数据
```

#### 4.1.4 股票日线基本数据表 (stock_daily_basic)
```sql
-- 已存在，包含估值和基本面数据
-- PE, PB, PS, 换手率, 市值等关键指标
```

#### 4.1.5 资金流向数据表 (stock_moneyflow)
```sql
-- 已存在，包含大单、中单、小单资金流向
-- 净流入额、各类资金买卖数据
```

#### 4.1.6 筹码分布数据表 (stock_cyq_perf)
```sql
-- 已存在，包含筹码分布和胜率数据
-- 成本分位数、加权平均成本、胜率等
```

#### 4.1.7 移动平均线数据表 (stock_ma_data)
```sql
-- 已存在，包含各周期均线数据
-- MA5, MA10, MA20, MA30, MA60, MA120
-- EMA指数移动平均线
```

#### 4.1.8 业务大宽表 (stock_business)
```sql
-- 已存在，整合了多维度数据
-- 基本面 + 技术面 + 资金面的综合数据
```

### 4.2 新增数据表设计

#### 4.2.1 新闻数据表 (news_data)
```sql
CREATE TABLE news_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title TEXT COMMENT '新闻标题',
    content TEXT COMMENT '新闻内容',
    source VARCHAR(100) COMMENT '新闻来源',
    publish_time TIMESTAMP COMMENT '发布时间',
    related_stocks JSON COMMENT '相关股票代码',
    sentiment_score DECIMAL(4,2) COMMENT '情感分数',
    vector_id VARCHAR(100) COMMENT '向量数据库ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻数据表';
```

#### 4.2.2 向量索引表 (vector_index)
```sql
CREATE TABLE vector_index (
    id INT AUTO_INCREMENT PRIMARY KEY,
    source_table VARCHAR(50) COMMENT '来源表名',
    source_id VARCHAR(100) COMMENT '来源记录ID',
    vector_id VARCHAR(100) COMMENT '向量数据库ID',
    data_type ENUM('stock', 'news', 'analysis') COMMENT '数据类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_source (source_table, source_id),
    INDEX idx_vector (vector_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='向量索引表';
```

## 5. 核心功能实现

### 5.1 数据访问模块

#### 5.1.1 MySQL数据库连接

#### 5.1.2 数据预处理模块

### 5.2 向量化和存储模块

#### 5.2.1 本地Ollama向量化

#### 5.2.2 MySQL向量索引管理

### 5.3 本地Ollama LLM调用模块

### 5.4 增强的RAG检索模块

### 5.5 主要业务逻辑

## 8. 高级特性设计

### 8.1 智能记忆衰减管理系统

#### 8.1.1 时间衰减策略

#### 8.1.2 多级归纳与摘要机制

### 8.2 智能反馈与强化学习机制

#### 8.2.1 预测准确性评估系统

#### 8.2.2 用户反馈收集系统

### 8.3 增量学习与模型优化

#### 8.3.1 本地模型增量训练

### 8.4 检索效率优化系统

#### 8.4.1 智能索引管理

## 9. 系统监控与运维

### 9.1 性能监控系统

这个设计文档提供了完整的系统架构和实现方案，您可以根据实际需求进行调整和优化。 