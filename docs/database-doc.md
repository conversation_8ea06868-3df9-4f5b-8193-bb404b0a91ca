## 数据库表
```
CREATE TABLE `stock_basic` (
  `ts_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'TS代码',
  `symbol` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '股票代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '股票名称',
  `area` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '地域',
  `industry` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属行业',
  `list_date` date DEFAULT NULL COMMENT '上市日期',
  PRIMARY KEY (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票公司基本信息表';

CREATE TABLE `stock_cyq_perf` (
  `ts_code` varchar(20) NOT NULL COMMENT '股票代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `his_low` decimal(10,2) DEFAULT NULL COMMENT '历史最低价',
  `his_high` decimal(10,2) DEFAULT NULL COMMENT '历史最高价',
  `cost_5pct` decimal(10,2) DEFAULT NULL COMMENT '5%成本分位',
  `cost_15pct` decimal(10,2) DEFAULT NULL COMMENT '15%成本分位',
  `cost_50pct` decimal(10,2) DEFAULT NULL COMMENT '50%成本分位',
  `cost_85pct` decimal(10,2) DEFAULT NULL COMMENT '85%成本分位',
  `cost_95pct` decimal(10,2) DEFAULT NULL COMMENT '95%成本分位',
  `weight_avg` decimal(10,2) DEFAULT NULL COMMENT '加权平均成本',
  `winner_rate` decimal(10,2) DEFAULT NULL COMMENT '胜率',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='每日筹码及胜率数据表';

CREATE TABLE `stock_daily_basic` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `close` decimal(10,2) DEFAULT NULL COMMENT '当日收盘价',
  `turnover_rate` decimal(10,2) DEFAULT NULL COMMENT '换手率（%）',
  `turnover_rate_f` decimal(10,2) DEFAULT NULL COMMENT '换手率（自由流通股）',
  `volume_ratio` decimal(10,2) DEFAULT NULL COMMENT '量比',
  `pe` decimal(10,2) DEFAULT NULL COMMENT '市盈率（总市值/净利润， 亏损的PE为空）',
  `pe_ttm` decimal(10,2) DEFAULT NULL COMMENT '市盈率（TTM，亏损的PE为空）',
  `pb` decimal(10,2) DEFAULT NULL COMMENT '市净率（总市值/净资产）',
  `ps` decimal(10,2) DEFAULT NULL COMMENT '市销率',
  `ps_ttm` decimal(10,2) DEFAULT NULL COMMENT '市销率（TTM）',
  `dv_ratio` decimal(10,2) DEFAULT NULL COMMENT '股息率 （%）',
  `dv_ttm` decimal(10,2) DEFAULT NULL COMMENT '股息率（TTM）（%）',
  `total_share` decimal(20,2) DEFAULT NULL COMMENT '总股本 （万股）',
  `float_share` decimal(20,2) DEFAULT NULL COMMENT '流通股本 （万股）',
  `free_share` decimal(20,2) DEFAULT NULL COMMENT '自由流通股本 （万）',
  `total_mv` decimal(20,2) DEFAULT NULL COMMENT '总市值 （万元）',
  `circ_mv` decimal(20,2) DEFAULT NULL COMMENT '流通市值（万元）',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票最新一天日线基本数据表';

CREATE TABLE `stock_daily_history` (
  `ts_code` varchar(20) NOT NULL COMMENT '股票代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `open` decimal(10,2) DEFAULT NULL COMMENT '开盘价',
  `high` decimal(10,2) DEFAULT NULL COMMENT '最高价',
  `low` decimal(10,2) DEFAULT NULL COMMENT '最低价',
  `close` decimal(10,2) DEFAULT NULL COMMENT '收盘价',
  `pre_close` decimal(10,2) DEFAULT NULL COMMENT '昨收价【除权价，前复权】',
  `change_c` decimal(10,2) DEFAULT NULL COMMENT '涨跌额',
  `pct_chg` decimal(10,2) DEFAULT NULL COMMENT '涨跌幅 【基于除权后的昨收计算的涨跌幅：（今收-除权昨收）/除权昨收】',
  `vol` bigint DEFAULT NULL COMMENT '成交量 （手）',
  `amount` decimal(20,2) DEFAULT NULL COMMENT '成交额 （千元）',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票日线行情历史数据表';

CREATE TABLE `stock_factor` (
  `ts_code` varchar(20) NOT NULL COMMENT '股票代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `close` decimal(10,2) DEFAULT NULL COMMENT '收盘价',
  `open` decimal(10,2) DEFAULT NULL COMMENT '开盘价',
  `high` decimal(10,2) DEFAULT NULL COMMENT '最高价',
  `low` decimal(10,2) DEFAULT NULL COMMENT '最低价',
  `pre_close` decimal(10,2) DEFAULT NULL COMMENT '昨收价',
  `change` decimal(10,2) DEFAULT NULL COMMENT '涨跌额',
  `pct_change` decimal(10,2) DEFAULT NULL COMMENT '涨跌幅',
  `vol` decimal(20,2) DEFAULT NULL COMMENT '成交量',
  `amount` decimal(20,2) DEFAULT NULL COMMENT '成交额',
  `adj_factor` decimal(10,2) DEFAULT NULL COMMENT '复权因子',
  `open_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权开盘价',
  `open_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权开盘价',
  `close_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权收盘价',
  `close_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权收盘价',
  `high_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权最高价',
  `high_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权最高价',
  `low_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权最低价',
  `low_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权最低价',
  `pre_close_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权昨收价',
  `pre_close_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权昨收价',
  `macd_dif` decimal(10,2) DEFAULT NULL COMMENT 'MACD DIF值',
  `macd_dea` decimal(10,2) DEFAULT NULL COMMENT 'MACD DEA值',
  `macd` decimal(10,2) DEFAULT NULL COMMENT 'MACD值',
  `kdj_k` decimal(10,2) DEFAULT NULL COMMENT 'KDJ K值',
  `kdj_d` decimal(10,2) DEFAULT NULL COMMENT 'KDJ D值',
  `kdj_j` decimal(10,2) DEFAULT NULL COMMENT 'KDJ J值',
  `rsi_6` decimal(10,2) DEFAULT NULL COMMENT 'RSI 6日',
  `rsi_12` decimal(10,2) DEFAULT NULL COMMENT 'RSI 12日',
  `rsi_24` decimal(10,2) DEFAULT NULL COMMENT 'RSI 24日',
  `boll_upper` decimal(10,2) DEFAULT NULL COMMENT '布林上轨',
  `boll_mid` decimal(10,2) DEFAULT NULL COMMENT '布林中轨',
  `boll_lower` decimal(10,2) DEFAULT NULL COMMENT '布林下轨',
  `cci` decimal(10,2) DEFAULT NULL COMMENT 'CCI指标',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票技术面因子数据表';

CREATE TABLE `stock_ma_data` (
  `ts_code` varchar(20) NOT NULL COMMENT '股票代码',
  `ma5` decimal(10,3) DEFAULT NULL COMMENT '5日移动平均线',
  `ma10` decimal(10,3) DEFAULT NULL COMMENT '10日移动平均线',
  `ma20` decimal(10,3) DEFAULT NULL COMMENT '20日移动平均线',
  `ma30` decimal(10,3) DEFAULT NULL COMMENT '30日移动平均线',
  `ma60` decimal(10,3) DEFAULT NULL COMMENT '60日移动平均线',
  `ma120` decimal(10,3) DEFAULT NULL COMMENT '120日移动平均线',
  `ema5` decimal(10,3) DEFAULT NULL COMMENT '5日指数移动平均线',
  `ema10` decimal(10,3) DEFAULT NULL COMMENT '10日指数移动平均线',
  `ema20` decimal(10,3) DEFAULT NULL COMMENT '20日指数移动平均线',
  `ema30` decimal(10,3) DEFAULT NULL COMMENT '30日指数移动平均线',
  `ema60` decimal(10,3) DEFAULT NULL COMMENT '60日指数移动平均线',
  `ema120` decimal(10,3) DEFAULT NULL COMMENT '120日指数移动平均线',
  PRIMARY KEY (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票移动平均线数据表';

CREATE TABLE `stock_moneyflow` (
  `ts_code` varchar(20) NOT NULL COMMENT '股票代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `buy_sm_vol` decimal(20,2) DEFAULT NULL COMMENT '小单买入量（手）',
  `buy_sm_amount` decimal(20,2) DEFAULT NULL COMMENT '小单买入金额（万元）',
  `sell_sm_vol` decimal(20,2) DEFAULT NULL COMMENT '小单卖出量（手）',
  `sell_sm_amount` decimal(20,2) DEFAULT NULL COMMENT '小单卖出金额（万元）',
  `buy_md_vol` decimal(20,2) DEFAULT NULL COMMENT '中单买入量（手）',
  `buy_md_amount` decimal(20,2) DEFAULT NULL COMMENT '中单买入金额（万元）',
  `sell_md_vol` decimal(20,2) DEFAULT NULL COMMENT '中单卖出量（手）',
  `sell_md_amount` decimal(20,2) DEFAULT NULL COMMENT '中单卖出金额（万元）',
  `buy_lg_vol` decimal(20,2) DEFAULT NULL COMMENT '大单买入量（手）',
  `buy_lg_amount` decimal(20,2) DEFAULT NULL COMMENT '大单买入金额（万元）',
  `sell_lg_vol` decimal(20,2) DEFAULT NULL COMMENT '大单卖出量（手）',
  `sell_lg_amount` decimal(20,2) DEFAULT NULL COMMENT '大单卖出金额（万元）',
  `buy_elg_vol` decimal(20,2) DEFAULT NULL COMMENT '特大单买入量（手）',
  `buy_elg_amount` decimal(20,2) DEFAULT NULL COMMENT '特大单买入金额（万元）',
  `sell_elg_vol` decimal(20,2) DEFAULT NULL COMMENT '特大单卖出量（手）',
  `sell_elg_amount` decimal(20,2) DEFAULT NULL COMMENT '特大单卖出金额（万元）',
  `net_mf_vol` decimal(20,2) DEFAULT NULL COMMENT '净流入量（手）',
  `net_mf_amount` decimal(20,2) DEFAULT NULL COMMENT '净流入额（万元）',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个股资金流向数据表';

CREATE TABLE `stock_business` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `stock_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '股票名称',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `daily_close` decimal(10,2) DEFAULT NULL COMMENT '当日收盘价',
  `turnover_rate` decimal(10,2) DEFAULT NULL COMMENT '换手率（%）',
  `turnover_rate_f` decimal(10,2) DEFAULT NULL COMMENT '换手率（自由流通股）',
  `volume_ratio` decimal(10,2) DEFAULT NULL COMMENT '量比',
  `pe` decimal(10,2) DEFAULT NULL COMMENT '市盈率（总市值/净利润， 亏损的PE为空）',
  `pe_ttm` decimal(10,2) DEFAULT NULL COMMENT '市盈率（TTM，亏损的PE为空）',
  `pb` decimal(10,2) DEFAULT NULL COMMENT '市净率（总市值/净资产）',
  `ps` decimal(10,2) DEFAULT NULL COMMENT '市销率',
  `ps_ttm` decimal(10,2) DEFAULT NULL COMMENT '市销率（TTM）',
  `dv_ratio` decimal(10,2) DEFAULT NULL COMMENT '股息率 （%）',
  `dv_ttm` decimal(10,2) DEFAULT NULL COMMENT '股息率（TTM）（%）',
  `total_share` decimal(20,2) DEFAULT NULL COMMENT '总股本 （万股）',
  `float_share` decimal(20,2) DEFAULT NULL COMMENT '流通股本 （万股）',
  `free_share` decimal(20,2) DEFAULT NULL COMMENT '自由流通股本 （万）',
  `total_mv` decimal(20,2) DEFAULT NULL COMMENT '总市值 （万元）',
  `circ_mv` decimal(20,2) DEFAULT NULL COMMENT '流通市值（万元）',
  `factor_open` decimal(10,2) DEFAULT NULL COMMENT '开盘价',
  `factor_high` decimal(10,2) DEFAULT NULL COMMENT '最高价',
  `factor_low` decimal(10,2) DEFAULT NULL COMMENT '最低价',
  `factor_pre_close` decimal(10,2) DEFAULT NULL COMMENT '昨收价',
  `factor_change` decimal(10,2) DEFAULT NULL COMMENT '涨跌额',
  `factor_pct_change` decimal(10,2) DEFAULT NULL COMMENT '涨跌幅',
  `factor_vol` decimal(20,2) DEFAULT NULL COMMENT '成交量',
  `factor_amount` decimal(20,2) DEFAULT NULL COMMENT '成交额',
  `factor_adj_factor` decimal(10,2) DEFAULT NULL COMMENT '复权因子',
  `factor_open_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权开盘价',
  `factor_open_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权开盘价',
  `factor_close_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权收盘价',
  `factor_close_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权收盘价',
  `factor_high_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权最高价',
  `factor_high_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权最高价',
  `factor_low_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权最低价',
  `factor_low_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权最低价',
  `factor_pre_close_hfq` decimal(10,2) DEFAULT NULL COMMENT '后复权昨收价',
  `factor_pre_close_qfq` decimal(10,2) DEFAULT NULL COMMENT '前复权昨收价',
  `factor_macd_dif` decimal(10,2) DEFAULT NULL COMMENT 'MACD DIF值',
  `factor_macd_dea` decimal(10,2) DEFAULT NULL COMMENT 'MACD DEA值',
  `factor_macd` decimal(10,2) DEFAULT NULL COMMENT 'MACD值',
  `factor_kdj_k` decimal(10,2) DEFAULT NULL COMMENT 'KDJ K值',
  `factor_kdj_d` decimal(10,2) DEFAULT NULL COMMENT 'KDJ D值',
  `factor_kdj_j` decimal(10,2) DEFAULT NULL COMMENT 'KDJ J值',
  `factor_rsi_6` decimal(10,2) DEFAULT NULL COMMENT 'RSI 6日',
  `factor_rsi_12` decimal(10,2) DEFAULT NULL COMMENT 'RSI 12日',
  `factor_rsi_24` decimal(10,2) DEFAULT NULL COMMENT 'RSI 24日',
  `factor_boll_upper` decimal(10,2) DEFAULT NULL COMMENT '布林上轨',
  `factor_boll_mid` decimal(10,2) DEFAULT NULL COMMENT '布林中轨',
  `factor_boll_lower` decimal(10,2) DEFAULT NULL COMMENT '布林下轨',
  `factor_cci` decimal(10,2) DEFAULT NULL COMMENT 'CCI指标',
  `moneyflow_pct_change` decimal(10,2) DEFAULT NULL COMMENT '涨跌幅',
  `moneyflow_latest` decimal(10,2) DEFAULT NULL COMMENT '最新价',
  `moneyflow_net_amount` decimal(20,2) DEFAULT NULL COMMENT '净流入额',
  `moneyflow_net_d5_amount` decimal(20,2) DEFAULT NULL COMMENT '5日净流入额',
  `moneyflow_buy_lg_amount` decimal(20,2) DEFAULT NULL COMMENT '大单买入额',
  `moneyflow_buy_lg_amount_rate` decimal(10,2) DEFAULT NULL COMMENT '大单买入额占比',
  `moneyflow_buy_md_amount` decimal(20,2) DEFAULT NULL COMMENT '中单买入额',
  `moneyflow_buy_md_amount_rate` decimal(10,2) DEFAULT NULL COMMENT '中单买入额占比',
  `moneyflow_buy_sm_amount` decimal(20,2) DEFAULT NULL COMMENT '小单买入额',
  `moneyflow_buy_sm_amount_rate` decimal(10,2) DEFAULT NULL COMMENT '小单买入额占比',
  `ma5` decimal(10,3) DEFAULT NULL COMMENT '5日移动平均线',
  `ma10` decimal(10,3) DEFAULT NULL COMMENT '10日移动平均线',
  `ma20` decimal(10,3) DEFAULT NULL COMMENT '20日移动平均线',
  `ma30` decimal(10,3) DEFAULT NULL COMMENT '30日移动平均线',
  `ma60` decimal(10,3) DEFAULT NULL COMMENT '60日移动平均线',
  `ma120` decimal(10,3) DEFAULT NULL COMMENT '120日移动平均线'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票业务大宽表';

CREATE TABLE `stock_income_statement` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `ann_date` varchar(8) DEFAULT NULL COMMENT '公告日期',
  `f_ann_date` varchar(8) DEFAULT NULL COMMENT '实际公告日期',
  `end_date` varchar(8) NOT NULL COMMENT '报告期',
  `report_type` varchar(10) DEFAULT NULL COMMENT '报告类型',
  `comp_type` varchar(10) DEFAULT NULL COMMENT '公司类型',
  `end_type` varchar(10) DEFAULT NULL COMMENT '报告期类型',
  `basic_eps` decimal(10,4) DEFAULT NULL COMMENT '基本每股收益',
  `diluted_eps` decimal(10,4) DEFAULT NULL COMMENT '稀释每股收益',
  `total_revenue` decimal(20,4) DEFAULT NULL COMMENT '营业总收入',
  `revenue` decimal(20,4) DEFAULT NULL COMMENT '营业收入',
  `int_income` decimal(20,4) DEFAULT NULL COMMENT '利息收入',
  `prem_earned` decimal(20,4) DEFAULT NULL COMMENT '已赚保费',
  `comm_income` decimal(20,4) DEFAULT NULL COMMENT '手续费及佣金收入',
  `n_commis_income` decimal(20,4) DEFAULT NULL COMMENT '手续费及佣金净收入',
  `n_oth_income` decimal(20,4) DEFAULT NULL COMMENT '其他经营净收益',
  `n_oth_b_income` decimal(20,4) DEFAULT NULL COMMENT '加:其他业务净收益',
  `prem_income` decimal(20,4) DEFAULT NULL COMMENT '保险业务收入',
  `out_prem` decimal(20,4) DEFAULT NULL COMMENT '减:分出保费',
  `une_prem_reser` decimal(20,4) DEFAULT NULL COMMENT '提取未到期责任准备金',
  `reins_income` decimal(20,4) DEFAULT NULL COMMENT '其中:分保费收入',
  `n_sec_tb_income` decimal(20,4) DEFAULT NULL COMMENT '代理买卖证券业务净收入',
  `n_sec_uw_income` decimal(20,4) DEFAULT NULL COMMENT '证券承销业务净收入',
  `n_asset_mg_income` decimal(20,4) DEFAULT NULL COMMENT '受托客户资产管理业务净收入',
  `oth_b_income` decimal(20,4) DEFAULT NULL COMMENT '其他业务收入',
  `fv_value_chg_gain` decimal(20,4) DEFAULT NULL COMMENT '加:公允价值变动净收益',
  `invest_income` decimal(20,4) DEFAULT NULL COMMENT '加:投资净收益',
  `ass_invest_income` decimal(20,4) DEFAULT NULL COMMENT '其中:对联营企业和合营企业的投资收益',
  `forex_gain` decimal(20,4) DEFAULT NULL COMMENT '加:汇兑净收益',
  `total_cogs` decimal(20,4) DEFAULT NULL COMMENT '营业总成本',
  `oper_cost` decimal(20,4) DEFAULT NULL COMMENT '减:营业成本',
  `int_exp` decimal(20,4) DEFAULT NULL COMMENT '减:利息支出',
  `comm_exp` decimal(20,4) DEFAULT NULL COMMENT '减:手续费及佣金支出',
  `biz_tax_surchg` decimal(20,4) DEFAULT NULL COMMENT '减:营业税金及附加',
  `sell_exp` decimal(20,4) DEFAULT NULL COMMENT '减:销售费用',
  `admin_exp` decimal(20,4) DEFAULT NULL COMMENT '减:管理费用',
  `fin_exp` decimal(20,4) DEFAULT NULL COMMENT '减:财务费用',
  `assets_impair_loss` decimal(20,4) DEFAULT NULL COMMENT '减:资产减值损失',
  `prem_refund` decimal(20,4) DEFAULT NULL COMMENT '退保金',
  `compens_payout` decimal(20,4) DEFAULT NULL COMMENT '赔付总支出',
  `reser_insur_liab` decimal(20,4) DEFAULT NULL COMMENT '提取保险责任准备金',
  `div_payt` decimal(20,4) DEFAULT NULL COMMENT '保户红利支出',
  `reins_exp` decimal(20,4) DEFAULT NULL COMMENT '分保费用',
  `oper_exp` decimal(20,4) DEFAULT NULL COMMENT '营业支出',
  `compens_payout_refu` decimal(20,4) DEFAULT NULL COMMENT '减:摊回赔付支出',
  `insur_reser_refu` decimal(20,4) DEFAULT NULL COMMENT '减:摊回保险责任准备金',
  `reins_cost_refund` decimal(20,4) DEFAULT NULL COMMENT '减:摊回分保费用',
  `other_bus_cost` decimal(20,4) DEFAULT NULL COMMENT '其他业务成本',
  `operate_profit` decimal(20,4) DEFAULT NULL COMMENT '营业利润',
  `non_oper_income` decimal(20,4) DEFAULT NULL COMMENT '加:营业外收入',
  `non_oper_exp` decimal(20,4) DEFAULT NULL COMMENT '减:营业外支出',
  `nca_disploss` decimal(20,4) DEFAULT NULL COMMENT '其中:减:非流动资产处置净损失',
  `total_profit` decimal(20,4) DEFAULT NULL COMMENT '利润总额',
  `income_tax` decimal(20,4) DEFAULT NULL COMMENT '所得税费用',
  `n_income` decimal(20,4) DEFAULT NULL COMMENT '净利润',
  `n_income_attr_p` decimal(20,4) DEFAULT NULL COMMENT '归属于母公司所有者的净利润',
  `minority_gain` decimal(20,4) DEFAULT NULL COMMENT '少数股东损益',
  `oth_compr_income` decimal(20,4) DEFAULT NULL COMMENT '其他综合收益',
  `t_compr_income` decimal(20,4) DEFAULT NULL COMMENT '综合收益总额',
  `compr_inc_attr_p` decimal(20,4) DEFAULT NULL COMMENT '归属于母公司所有者的综合收益总额',
  `compr_inc_attr_m_s` decimal(20,4) DEFAULT NULL COMMENT '归属于少数股东的综合收益总额',
  `ebit` decimal(20,4) DEFAULT NULL COMMENT '息税前利润',
  `ebitda` decimal(20,4) DEFAULT NULL COMMENT '息税折旧摊销前利润',
  `insurance_exp` decimal(20,4) DEFAULT NULL COMMENT '保险业务支出',
  `undist_profit` decimal(20,4) DEFAULT NULL COMMENT '年初未分配利润',
  `distable_profit` decimal(20,4) DEFAULT NULL COMMENT '可分配利润',
  `rd_exp` decimal(20,4) DEFAULT NULL COMMENT '研发费用',
  `fin_exp_int_exp` decimal(20,4) DEFAULT NULL COMMENT '财务费用:利息费用',
  `fin_exp_int_inc` decimal(20,4) DEFAULT NULL COMMENT '财务费用:利息收入',
  `transfer_surplus_rese` decimal(20,4) DEFAULT NULL COMMENT '转入盈余公积',
  `transfer_housing_imprest` decimal(20,4) DEFAULT NULL COMMENT '转入住房周转金',
  `transfer_oth` decimal(20,4) DEFAULT NULL COMMENT '转入其他',
  `adj_lossgain` decimal(20,4) DEFAULT NULL COMMENT '调整以前年度损益',
  `withdra_legal_surplus` decimal(20,4) DEFAULT NULL COMMENT '提取法定盈余公积',
  `withdra_legal_pubfund` decimal(20,4) DEFAULT NULL COMMENT '提取法定公益金',
  `withdra_biz_devfund` decimal(20,4) DEFAULT NULL COMMENT '提取企业发展基金',
  `withdra_rese_fund` decimal(20,4) DEFAULT NULL COMMENT '提取储备基金',
  `withdra_oth_ersu` decimal(20,4) DEFAULT NULL COMMENT '提取其他',
  `workers_welfare` decimal(20,4) DEFAULT NULL COMMENT '职工奖金福利',
  `distr_profit_shrhder` decimal(20,4) DEFAULT NULL COMMENT '可供股东分配的利润',
  `prfshare_payable_dvd` decimal(20,4) DEFAULT NULL COMMENT '应付优先股股利',
  `comshare_payable_dvd` decimal(20,4) DEFAULT NULL COMMENT '应付普通股股利',
  `capit_comstock_div` decimal(20,4) DEFAULT NULL COMMENT '转作股本的普通股股利',
  `continued_net_profit` decimal(20,4) DEFAULT NULL COMMENT '持续经营净利润',
  `update_flag` varchar(1) DEFAULT NULL COMMENT '更新标识',
  PRIMARY KEY (`ts_code`,`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='利润表数据表';

CREATE TABLE `stock_balance_sheet` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `ann_date` varchar(8) DEFAULT NULL COMMENT '公告日期',
  `f_ann_date` varchar(8) DEFAULT NULL COMMENT '实际公告日期',
  `end_date` varchar(8) NOT NULL COMMENT '报告期',
  `report_type` varchar(10) DEFAULT NULL COMMENT '报告类型',
  `comp_type` varchar(10) DEFAULT NULL COMMENT '公司类型',
  `end_type` varchar(10) DEFAULT NULL COMMENT '报告期类型',
  `total_share` decimal(20,4) DEFAULT NULL COMMENT '期末总股本',
  `cap_rese` decimal(20,4) DEFAULT NULL COMMENT '资本公积金',
  `undistr_porfit` decimal(20,4) DEFAULT NULL COMMENT '未分配利润',
  `surplus_rese` decimal(20,4) DEFAULT NULL COMMENT '盈余公积金',
  `special_rese` decimal(20,4) DEFAULT NULL COMMENT '专项储备',
  `money_cap` decimal(20,4) DEFAULT NULL COMMENT '货币资金',
  `trad_asset` decimal(20,4) DEFAULT NULL COMMENT '交易性金融资产',
  `notes_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收票据',
  `accounts_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收账款',
  `oth_receiv` decimal(20,4) DEFAULT NULL COMMENT '其他应收款',
  `prepayment` decimal(20,4) DEFAULT NULL COMMENT '预付款项',
  `div_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收股利',
  `int_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收利息',
  `inventories` decimal(20,4) DEFAULT NULL COMMENT '存货',
  `amor_exp` decimal(20,4) DEFAULT NULL COMMENT '长期待摊费用',
  `nca_within_1y` decimal(20,4) DEFAULT NULL COMMENT '一年内到期的非流动资产',
  `sett_rsrv` decimal(20,4) DEFAULT NULL COMMENT '结算备付金',
  `loanto_oth_bank_fi` decimal(20,4) DEFAULT NULL COMMENT '拆出资金',
  `premium_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收保费',
  `reinsur_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收分保账款',
  `reinsur_res_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收分保合同准备金',
  `pur_resale_fa` decimal(20,4) DEFAULT NULL COMMENT '买入返售金融资产',
  `oth_cur_assets` decimal(20,4) DEFAULT NULL COMMENT '其他流动资产',
  `total_cur_assets` decimal(20,4) DEFAULT NULL COMMENT '流动资产合计',
  `fa_avail_for_sale` decimal(20,4) DEFAULT NULL COMMENT '可供出售金融资产',
  `htm_invest` decimal(20,4) DEFAULT NULL COMMENT '持有至到期投资',
  `lt_eqt_invest` decimal(20,4) DEFAULT NULL COMMENT '长期股权投资',
  `invest_real_estate` decimal(20,4) DEFAULT NULL COMMENT '投资性房地产',
  `time_deposits` decimal(20,4) DEFAULT NULL COMMENT '定期存款',
  `oth_assets` decimal(20,4) DEFAULT NULL COMMENT '其他资产',
  `lt_rec` decimal(20,4) DEFAULT NULL COMMENT '长期应收款',
  `fix_assets` decimal(20,4) DEFAULT NULL COMMENT '固定资产',
  `cip` decimal(20,4) DEFAULT NULL COMMENT '在建工程',
  `const_materials` decimal(20,4) DEFAULT NULL COMMENT '工程物资',
  `fixed_assets_disp` decimal(20,4) DEFAULT NULL COMMENT '固定资产清理',
  `produc_bio_assets` decimal(20,4) DEFAULT NULL COMMENT '生产性生物资产',
  `oil_and_gas_assets` decimal(20,4) DEFAULT NULL COMMENT '油气资产',
  `intan_assets` decimal(20,4) DEFAULT NULL COMMENT '无形资产',
  `r_and_d` decimal(20,4) DEFAULT NULL COMMENT '开发支出',
  `goodwill` decimal(20,4) DEFAULT NULL COMMENT '商誉',
  `lt_amor_exp` decimal(20,4) DEFAULT NULL COMMENT '长期待摊费用',
  `defer_tax_assets` decimal(20,4) DEFAULT NULL COMMENT '递延所得税资产',
  `decr_in_disbur` decimal(20,4) DEFAULT NULL COMMENT '发放贷款及垫款',
  `oth_nca` decimal(20,4) DEFAULT NULL COMMENT '其他非流动资产',
  `total_nca` decimal(20,4) DEFAULT NULL COMMENT '非流动资产合计',
  `cash_reser_cb` decimal(20,4) DEFAULT NULL COMMENT '现金及存放中央银行款项',
  `depos_in_oth_bfi` decimal(20,4) DEFAULT NULL COMMENT '存放同业和其它金融机构款项',
  `prec_metals` decimal(20,4) DEFAULT NULL COMMENT '贵金属',
  `deriv_assets` decimal(20,4) DEFAULT NULL COMMENT '衍生金融资产',
  `rr_reins_une_prem` decimal(20,4) DEFAULT NULL COMMENT '应收分保未到期责任准备金',
  `rr_reins_outstd_cla` decimal(20,4) DEFAULT NULL COMMENT '应收分保未决赔款准备金',
  `rr_reins_lins_liab` decimal(20,4) DEFAULT NULL COMMENT '应收分保寿险责任准备金',
  `rr_reins_lthins_liab` decimal(20,4) DEFAULT NULL COMMENT '应收分保长期健康险责任准备金',
  `refund_depos` decimal(20,4) DEFAULT NULL COMMENT '存出保证金',
  `ph_pledge_loans` decimal(20,4) DEFAULT NULL COMMENT '保户质押贷款',
  `refund_cap_depos` decimal(20,4) DEFAULT NULL COMMENT '存出资本保证金',
  `indep_acct_assets` decimal(20,4) DEFAULT NULL COMMENT '独立账户资产',
  `client_depos` decimal(20,4) DEFAULT NULL COMMENT '其中：客户资金存款',
  `client_prov` decimal(20,4) DEFAULT NULL COMMENT '其中：客户备付金',
  `transac_seat_fee` decimal(20,4) DEFAULT NULL COMMENT '其中:交易席位费',
  `invest_as_receiv` decimal(20,4) DEFAULT NULL COMMENT '应收款项类投资',
  `total_assets` decimal(20,4) DEFAULT NULL COMMENT '资产总计',
  `lt_borr` decimal(20,4) DEFAULT NULL COMMENT '长期借款',
  `st_borr` decimal(20,4) DEFAULT NULL COMMENT '短期借款',
  `cb_borr` decimal(20,4) DEFAULT NULL COMMENT '向中央银行借款',
  `depos_ib_deposits` decimal(20,4) DEFAULT NULL COMMENT '吸收存款及同业存放',
  `loan_oth_bank` decimal(20,4) DEFAULT NULL COMMENT '拆入资金',
  `trading_fl` decimal(20,4) DEFAULT NULL COMMENT '交易性金融负债',
  `notes_payable` decimal(20,4) DEFAULT NULL COMMENT '应付票据',
  `acct_payable` decimal(20,4) DEFAULT NULL COMMENT '应付账款',
  `adv_receipts` decimal(20,4) DEFAULT NULL COMMENT '预收款项',
  `sold_for_repur_fa` decimal(20,4) DEFAULT NULL COMMENT '卖出回购金融资产款',
  `comm_payable` decimal(20,4) DEFAULT NULL COMMENT '应付手续费及佣金',
  `payroll_payable` decimal(20,4) DEFAULT NULL COMMENT '应付职工薪酬',
  `taxes_payable` decimal(20,4) DEFAULT NULL COMMENT '应交税费',
  `int_payable` decimal(20,4) DEFAULT NULL COMMENT '应付利息',
  `div_payable` decimal(20,4) DEFAULT NULL COMMENT '应付股利',
  `oth_payable` decimal(20,4) DEFAULT NULL COMMENT '其他应付款',
  `acc_exp` decimal(20,4) DEFAULT NULL COMMENT '预提费用',
  `deferred_inc` decimal(20,4) DEFAULT NULL COMMENT '递延收益',
  `st_bonds_payable` decimal(20,4) DEFAULT NULL COMMENT '应付短期债券',
  `payable_to_reinsurer` decimal(20,4) DEFAULT NULL COMMENT '应付分保账款',
  `rsrv_insur_cont` decimal(20,4) DEFAULT NULL COMMENT '保险合同准备金',
  `acting_trading_sec` decimal(20,4) DEFAULT NULL COMMENT '代理买卖证券款',
  `acting_uw_sec` decimal(20,4) DEFAULT NULL COMMENT '代理承销证券款',
  `non_cur_liab_due_1y` decimal(20,4) DEFAULT NULL COMMENT '一年内到期的非流动负债',
  `oth_cur_liab` decimal(20,4) DEFAULT NULL COMMENT '其他流动负债',
  `total_cur_liab` decimal(20,4) DEFAULT NULL COMMENT '流动负债合计',
  `bond_payable` decimal(20,4) DEFAULT NULL COMMENT '应付债券',
  `lt_payable` decimal(20,4) DEFAULT NULL COMMENT '长期应付款',
  `specific_payables` decimal(20,4) DEFAULT NULL COMMENT '专项应付款',
  `estimated_liab` decimal(20,4) DEFAULT NULL COMMENT '预计负债',
  `defer_tax_liab` decimal(20,4) DEFAULT NULL COMMENT '递延所得税负债',
  `defer_inc_non_cur_liab` decimal(20,4) DEFAULT NULL COMMENT '递延收益-非流动负债',
  `oth_ncl` decimal(20,4) DEFAULT NULL COMMENT '其他非流动负债',
  `total_ncl` decimal(20,4) DEFAULT NULL COMMENT '非流动负债合计',
  `depos_oth_bfi` decimal(20,4) DEFAULT NULL COMMENT '同业和其它金融机构存放款项',
  `deriv_liab` decimal(20,4) DEFAULT NULL COMMENT '衍生金融负债',
  `depos` decimal(20,4) DEFAULT NULL COMMENT '吸收存款',
  `agency_bus_liab` decimal(20,4) DEFAULT NULL COMMENT '代理业务负债',
  `oth_liab` decimal(20,4) DEFAULT NULL COMMENT '其他负债',
  `prem_receiv_adva` decimal(20,4) DEFAULT NULL COMMENT '预收保费',
  `depos_received` decimal(20,4) DEFAULT NULL COMMENT '存入保证金',
  `ph_invest` decimal(20,4) DEFAULT NULL COMMENT '保户储金及投资款',
  `reser_une_prem` decimal(20,4) DEFAULT NULL COMMENT '未到期责任准备金',
  `reser_outstd_claims` decimal(20,4) DEFAULT NULL COMMENT '未决赔款准备金',
  `reser_lins_liab` decimal(20,4) DEFAULT NULL COMMENT '寿险责任准备金',
  `reser_lthins_liab` decimal(20,4) DEFAULT NULL COMMENT '长期健康险责任准备金',
  `indept_acc_liab` decimal(20,4) DEFAULT NULL COMMENT '独立账户负债',
  `pledge_borr` decimal(20,4) DEFAULT NULL COMMENT '其中：质押借款',
  `indem_payable` decimal(20,4) DEFAULT NULL COMMENT '应付赔付款',
  `policy_div_payable` decimal(20,4) DEFAULT NULL COMMENT '应付保单红利',
  `total_liab` decimal(20,4) DEFAULT NULL COMMENT '负债合计',
  `treasury_share` decimal(20,4) DEFAULT NULL COMMENT '减:库存股',
  `ordin_risk_reser` decimal(20,4) DEFAULT NULL COMMENT '一般风险准备',
  `forex_differ` decimal(20,4) DEFAULT NULL COMMENT '外币报表折算差额',
  `invest_loss_unconf` decimal(20,4) DEFAULT NULL COMMENT '未确认投资损失',
  `minority_int` decimal(20,4) DEFAULT NULL COMMENT '少数股东权益',
  `total_hldr_eqy_exc_min_int` decimal(20,4) DEFAULT NULL COMMENT '股东权益合计(不含少数股东权益)',
  `total_hldr_eqy_inc_min_int` decimal(20,4) DEFAULT NULL COMMENT '股东权益合计(含少数股东权益)',
  `total_liab_hldr_eqy` decimal(20,4) DEFAULT NULL COMMENT '负债及股东权益总计',
  `lt_payroll_payable` decimal(20,4) DEFAULT NULL COMMENT '长期应付职工薪酬',
  `oth_comp_income` decimal(20,4) DEFAULT NULL COMMENT '其他综合收益',
  `oth_eqt_tools` decimal(20,4) DEFAULT NULL COMMENT '其他权益工具',
  `oth_eqt_tools_p_shr` decimal(20,4) DEFAULT NULL COMMENT '其他权益工具(优先股)',
  `lending_funds` decimal(20,4) DEFAULT NULL COMMENT '融出资金',
  `acc_receivable` decimal(20,4) DEFAULT NULL COMMENT '应收款项',
  `st_fin_payable` decimal(20,4) DEFAULT NULL COMMENT '应付短期融资款',
  `payables` decimal(20,4) DEFAULT NULL COMMENT '应付款项',
  `hfs_assets` decimal(20,4) DEFAULT NULL COMMENT '持有待售资产',
  `hfs_sales` decimal(20,4) DEFAULT NULL COMMENT '持有待售负债',
  `cost_fin_assets` decimal(20,4) DEFAULT NULL COMMENT '以摊余成本计量的金融资产',
  `fair_value_fin_assets` decimal(20,4) DEFAULT NULL COMMENT '以公允价值计量且其变动计入其他综合收益的金融资产',
  `contract_assets` decimal(20,4) DEFAULT NULL COMMENT '合同资产',
  `contract_liab` decimal(20,4) DEFAULT NULL COMMENT '合同负债',
  `accounts_receiv_bill` decimal(20,4) DEFAULT NULL COMMENT '应收票据及应收账款',
  `accounts_pay` decimal(20,4) DEFAULT NULL COMMENT '应付票据及应付账款',
  `oth_rcv_total` decimal(20,4) DEFAULT NULL COMMENT '其他应收款(合计)',
  `fix_assets_total` decimal(20,4) DEFAULT NULL COMMENT '固定资产(合计)',
  `cip_total` decimal(20,4) DEFAULT NULL COMMENT '在建工程(合计)',
  `oth_pay_total` decimal(20,4) DEFAULT NULL COMMENT '其他应付款(合计)',
  `long_pay_total` decimal(20,4) DEFAULT NULL COMMENT '长期应付款(合计)',
  `debt_invest` decimal(20,4) DEFAULT NULL COMMENT '债权投资',
  `oth_debt_invest` decimal(20,4) DEFAULT NULL COMMENT '其他债权投资',
  `update_flag` varchar(1) DEFAULT NULL COMMENT '更新标识',
  PRIMARY KEY (`ts_code`,`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资产负债表数据表';

CREATE TABLE `stock_cash_flow` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `ann_date` varchar(8) DEFAULT NULL COMMENT '公告日期',
  `f_ann_date` varchar(8) DEFAULT NULL COMMENT '实际公告日期',
  `end_date` varchar(8) NOT NULL COMMENT '报告期',
  `comp_type` varchar(10) DEFAULT NULL COMMENT '公司类型',
  `report_type` varchar(10) DEFAULT NULL COMMENT '报告类型',
  `end_type` varchar(10) DEFAULT NULL COMMENT '报告期类型',
  `net_profit` decimal(20,4) DEFAULT NULL COMMENT '净利润',
  `finan_exp` decimal(20,4) DEFAULT NULL COMMENT '财务费用',
  `c_fr_sale_sg` decimal(20,4) DEFAULT NULL COMMENT '销售商品、提供劳务收到的现金',
  `recp_tax_rends` decimal(20,4) DEFAULT NULL COMMENT '收到的税费返还',
  `n_depos_incr_fi` decimal(20,4) DEFAULT NULL COMMENT '客户存款和同业存放款项净增加额',
  `n_incr_loans_cb` decimal(20,4) DEFAULT NULL COMMENT '向中央银行借款净增加额',
  `n_inc_borr_oth_fi` decimal(20,4) DEFAULT NULL COMMENT '向其他金融机构拆入资金净增加额',
  `prem_fr_orig_contr` decimal(20,4) DEFAULT NULL COMMENT '收到原保险合同保费取得的现金',
  `n_incr_insured_dep` decimal(20,4) DEFAULT NULL COMMENT '保户储金净增加额',
  `n_reinsur_prem` decimal(20,4) DEFAULT NULL COMMENT '收到再保业务现金净额',
  `n_incr_disp_tfa` decimal(20,4) DEFAULT NULL COMMENT '处置交易性金融资产净增加额',
  `ifc_cash_incr` decimal(20,4) DEFAULT NULL COMMENT '收取利息和手续费净增加额',
  `n_incr_disp_faas` decimal(20,4) DEFAULT NULL COMMENT '处置可供出售金融资产净增加额',
  `n_incr_loans_oth_bank` decimal(20,4) DEFAULT NULL COMMENT '拆入资金净增加额',
  `n_cap_incr_repur` decimal(20,4) DEFAULT NULL COMMENT '回购业务资金净增加额',
  `c_fr_oth_operate_a` decimal(20,4) DEFAULT NULL COMMENT '收到其他与经营活动有关的现金',
  `c_inf_fr_operate_a` decimal(20,4) DEFAULT NULL COMMENT '经营活动现金流入小计',
  `c_paid_goods_s` decimal(20,4) DEFAULT NULL COMMENT '购买商品、接受劳务支付的现金',
  `c_paid_to_for_empl` decimal(20,4) DEFAULT NULL COMMENT '支付给职工以及为职工支付的现金',
  `c_paid_for_taxes` decimal(20,4) DEFAULT NULL COMMENT '支付的各项税费',
  `n_incr_clt_loan_adv` decimal(20,4) DEFAULT NULL COMMENT '客户贷款及垫款净增加额',
  `n_incr_dep_cbob` decimal(20,4) DEFAULT NULL COMMENT '存放央行和同业款项净增加额',
  `c_pay_claims_orig_inco` decimal(20,4) DEFAULT NULL COMMENT '支付原保险合同赔付款项的现金',
  `pay_handling_chrg` decimal(20,4) DEFAULT NULL COMMENT '支付手续费的现金',
  `pay_comm_insur_plcy` decimal(20,4) DEFAULT NULL COMMENT '支付保单红利的现金',
  `oth_cash_pay_oper_act` decimal(20,4) DEFAULT NULL COMMENT '支付其他与经营活动有关的现金',
  `st_cash_out_act` decimal(20,4) DEFAULT NULL COMMENT '经营活动现金流出小计',
  `n_cashflow_act` decimal(20,4) DEFAULT NULL COMMENT '经营活动产生的现金流量净额',
  `oth_recp_ral_inv_act` decimal(20,4) DEFAULT NULL COMMENT '收到其他与投资活动有关的现金',
  `c_disp_withdrwl_invest` decimal(20,4) DEFAULT NULL COMMENT '收回投资收到的现金',
  `c_recp_return_invest` decimal(20,4) DEFAULT NULL COMMENT '取得投资收益收到的现金',
  `n_recp_disp_fiolta` decimal(20,4) DEFAULT NULL COMMENT '处置固定资产无形资产和其他长期资产收回的现金净额',
  `n_recp_disp_sobu` decimal(20,4) DEFAULT NULL COMMENT '处置子公司及其他营业单位收到的现金净额',
  `stot_inflows_inv_act` decimal(20,4) DEFAULT NULL COMMENT '投资活动现金流入小计',
  `c_pay_acq_const_fiolta` decimal(20,4) DEFAULT NULL COMMENT '购建固定资产无形资产和其他长期资产支付的现金',
  `c_paid_invest` decimal(20,4) DEFAULT NULL COMMENT '投资支付的现金',
  `n_disp_subs_oth_biz` decimal(20,4) DEFAULT NULL COMMENT '取得子公司及其他营业单位支付的现金净额',
  `oth_pay_ral_inv_act` decimal(20,4) DEFAULT NULL COMMENT '支付其他与投资活动有关的现金',
  `n_incr_pledge_loan` decimal(20,4) DEFAULT NULL COMMENT '质押贷款净增加额',
  `stot_out_inv_act` decimal(20,4) DEFAULT NULL COMMENT '投资活动现金流出小计',
  `n_cashflow_inv_act` decimal(20,4) DEFAULT NULL COMMENT '投资活动产生的现金流量净额',
  `c_recp_borrow` decimal(20,4) DEFAULT NULL COMMENT '取得借款收到的现金',
  `proc_issue_bonds` decimal(20,4) DEFAULT NULL COMMENT '发行债券收到的现金',
  `oth_cash_recp_ral_fnc_act` decimal(20,4) DEFAULT NULL COMMENT '收到其他与筹资活动有关的现金',
  `stot_cash_in_fnc_act` decimal(20,4) DEFAULT NULL COMMENT '筹资活动现金流入小计',
  `free_cashflow` decimal(20,4) DEFAULT NULL COMMENT '企业自由现金流量',
  `c_prepay_amt_borr` decimal(20,4) DEFAULT NULL COMMENT '偿还债务支付的现金',
  `c_pay_dist_dpcp_int_exp` decimal(20,4) DEFAULT NULL COMMENT '分配股利、利润或偿付利息支付的现金',
  `incl_dvd_profit_paid_sc_ms` decimal(20,4) DEFAULT NULL COMMENT '其中:子公司支付给少数股东的股利、利润',
  `oth_cashpay_ral_fnc_act` decimal(20,4) DEFAULT NULL COMMENT '支付其他与筹资活动有关的现金',
  `stot_cashout_fnc_act` decimal(20,4) DEFAULT NULL COMMENT '筹资活动现金流出小计',
  `n_cash_flows_fnc_act` decimal(20,4) DEFAULT NULL COMMENT '筹资活动产生的现金流量净额',
  `eff_fx_flu_cash` decimal(20,4) DEFAULT NULL COMMENT '汇率变动对现金的影响',
  `n_incr_cash_cash_equ` decimal(20,4) DEFAULT NULL COMMENT '现金及现金等价物净增加额',
  `c_cash_equ_beg_period` decimal(20,4) DEFAULT NULL COMMENT '期初现金及现金等价物余额',
  `c_cash_equ_end_period` decimal(20,4) DEFAULT NULL COMMENT '期末现金及现金等价物余额',
  `c_recp_cap_contrib` decimal(20,4) DEFAULT NULL COMMENT '吸收投资收到的现金',
  `incl_cash_rec_saims` decimal(20,4) DEFAULT NULL COMMENT '其中:子公司吸收少数股东投资收到的现金',
  `uncon_invest_loss` decimal(20,4) DEFAULT NULL COMMENT '未确认投资损失',
  `prov_depr_assets` decimal(20,4) DEFAULT NULL COMMENT '加:资产减值准备',
  `depr_fa_coga_dpba` decimal(20,4) DEFAULT NULL COMMENT '固定资产折旧、油气资产折耗、生产性生物资产折旧',
  `amort_intang_assets` decimal(20,4) DEFAULT NULL COMMENT '无形资产摊销',
  `lt_amort_deferred_exp` decimal(20,4) DEFAULT NULL COMMENT '长期待摊费用摊销',
  `decr_deferred_exp` decimal(20,4) DEFAULT NULL COMMENT '待摊费用减少',
  `incr_acc_exp` decimal(20,4) DEFAULT NULL COMMENT '预提费用增加',
  `loss_disp_fiolta` decimal(20,4) DEFAULT NULL COMMENT '处置固定、无形资产和其他长期资产的损失',
  `loss_scr_fa` decimal(20,4) DEFAULT NULL COMMENT '固定资产报废损失',
  `loss_fv_chg` decimal(20,4) DEFAULT NULL COMMENT '公允价值变动损失',
  `invest_loss` decimal(20,4) DEFAULT NULL COMMENT '投资损失',
  `decr_def_inc_tax_assets` decimal(20,4) DEFAULT NULL COMMENT '递延所得税资产减少',
  `incr_def_inc_tax_liab` decimal(20,4) DEFAULT NULL COMMENT '递延所得税负债增加',
  `decr_inventories` decimal(20,4) DEFAULT NULL COMMENT '存货的减少',
  `decr_oper_payable` decimal(20,4) DEFAULT NULL COMMENT '经营性应收项目的减少',
  `incr_oper_payable` decimal(20,4) DEFAULT NULL COMMENT '经营性应付项目的增加',
  `others` decimal(20,4) DEFAULT NULL COMMENT '其他',
  `im_net_cashflow_oper_act` decimal(20,4) DEFAULT NULL COMMENT '经营活动产生的现金流量净额(间接法)',
  `conv_debt_into_cap` decimal(20,4) DEFAULT NULL COMMENT '债务转为资本',
  `conv_copbonds_due_within_1y` decimal(20,4) DEFAULT NULL COMMENT '一年内到期的可转换公司债券',
  `fa_fnc_leases` decimal(20,4) DEFAULT NULL COMMENT '融资租入固定资产',
  `im_n_incr_cash_equ` decimal(20,4) DEFAULT NULL COMMENT '现金及现金等价物净增加额(间接法)',
  `net_dism_capital_add` decimal(20,4) DEFAULT NULL COMMENT '拆出资金净增加额',
  `net_cash_rece_sec` decimal(20,4) DEFAULT NULL COMMENT '代理买卖证券收到的现金净额',
  `credit_impa_loss` decimal(20,4) DEFAULT NULL COMMENT '信用减值损失',
  `use_right_asset_dep` decimal(20,4) DEFAULT NULL COMMENT '使用权资产折旧',
  `oth_loss_asset` decimal(20,4) DEFAULT NULL COMMENT '其他资产减值损失',
  `end_bal_cash` decimal(20,4) DEFAULT NULL COMMENT '现金的期末余额',
  `beg_bal_cash` decimal(20,4) DEFAULT NULL COMMENT '现金的期初余额',
  `end_bal_cash_equ` decimal(20,4) DEFAULT NULL COMMENT '现金等价物的期末余额',
  `beg_bal_cash_equ` decimal(20,4) DEFAULT NULL COMMENT '现金等价物的期初余额',
  `update_flag` varchar(1) DEFAULT NULL COMMENT '更新标识',
  PRIMARY KEY (`ts_code`,`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='现金流量表数据表';

CREATE TABLE `stock_fina_mainbz` (
  `ts_code` varchar(20) NOT NULL COMMENT '股票代码',
  `end_date` date NOT NULL COMMENT '报告期',
  `bz_item` varchar(100) NOT NULL COMMENT '主营业务项目',
  `bz_code` varchar(50) DEFAULT NULL COMMENT '业务代码',
  `bz_sales` decimal(20,4) DEFAULT NULL COMMENT '主营业务收入(元)',
  `bz_profit` decimal(20,4) DEFAULT NULL COMMENT '主营业务利润(元)',
  `bz_cost` decimal(20,4) DEFAULT NULL COMMENT '主营业务成本(元)',
  `curr_type` varchar(10) DEFAULT NULL COMMENT '货币代码'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='主营业务构成数据表';

CREATE TABLE `stock_top10_floatholders` (
  `ts_code` varchar(20) NOT NULL COMMENT '股票代码',
  `ann_date` date DEFAULT NULL COMMENT '公告日期',
  `end_date` date NOT NULL COMMENT '报告期',
  `holder_name` varchar(100) NOT NULL COMMENT '股东名称',
  `hold_amount` decimal(20,4) DEFAULT NULL COMMENT '持股数量（股）',
  `hold_ratio` decimal(10,4) DEFAULT NULL COMMENT '持股比例',
  `hold_float_ratio` decimal(10,4) DEFAULT NULL COMMENT '流通股份持股比例',
  `hold_change` decimal(20,4) DEFAULT NULL COMMENT '持股变化（股）',
  `holder_type` varchar(50) DEFAULT NULL COMMENT '股东类型'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='前十大流通股东数据表';
```
---

## 数据库连接信息
```
    _host = 'localhost'       # MySQL主机
    _user = 'root'   # MySQL用户名
    _password = 'root'  # MySQL密码
    _database = 'stock_cursor'  # MySQL数据库名
    _charset = 'utf8mb4'
```
---

# ollama环境：
```
http://localhost:11434/
```

# ollama模型和向量数据库
```
NAME                              ID              SIZE      MODIFIED     
bge-large:latest                  b3d71c928059    670 MB    3 months ago    
bge-m3:latest                     790764642607    1.2 GB    3 months ago    
nomic-embed-text:latest           0a109f422b47    274 MB    4 months ago    
snowflake-arctic-embed2:latest    5de93a84837d    1.2 GB    4 months ago    
deepseek-r1:latest                0a8c26691023    4.7 GB    5 months ago        
qwen2.5-coder:latest              2b0496514337    4.7 GB    7 months ago    
qwen2.5:latest                    845dbda0ea48    4.7 GB    7 months ago 
```

