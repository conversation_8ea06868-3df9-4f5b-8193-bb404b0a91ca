"""
简化的Text2SQL测试
"""
from text2sql.schema_parser import schema_parser
from text2sql.sql_generator import sql_generator

def test_schema_basic():
    """测试基础Schema功能"""
    print("=== 测试Schema基础功能 ===")
    
    try:
        # 测试获取表信息
        table_info = schema_parser.get_table_info("stock_basic")
        print(f"✅ stock_basic表信息获取成功")
        print(f"描述: {table_info.get('description', '')}")
        print(f"字段数: {len(table_info.get('columns', []))}")
        
        # 测试相关表查询
        related_tables = schema_parser.get_related_tables(["股票", "价格"])
        print(f"✅ 相关表查询成功: {related_tables}")
        
    except Exception as e:
        print(f"❌ Schema测试失败: {e}")

def test_sql_generation_basic():
    """测试基础SQL生成"""
    print("\n=== 测试基础SQL生成 ===")
    
    try:
        # 测试简单查询
        queries = [
            "查询股票基本信息",
            "查询平安银行",
            "查询银行股票"
        ]
        
        for query in queries:
            print(f"\n测试查询: {query}")
            
            # 只测试查询意图分析，不调用LLM
            intent = sql_generator._analyze_query_intent(query)
            print(f"查询意图: {intent}")
            
            # 测试关键词提取
            keywords = []
            if "股票" in query:
                keywords.append("股票")
            if "银行" in query:
                keywords.append("银行")
            if "平安" in query:
                keywords.append("平安")
            
            print(f"关键词: {keywords}")
            
            # 建议使用的表
            if "基本信息" in query or "股票" in query:
                suggested_table = "stock_basic"
            elif "价格" in query:
                suggested_table = "stock_daily_history"
            else:
                suggested_table = "stock_basic"
            
            print(f"建议表: {suggested_table}")
        
        print(f"\n✅ 基础SQL生成测试完成")
        
    except Exception as e:
        print(f"❌ SQL生成测试失败: {e}")

def test_query_patterns():
    """测试查询模式识别"""
    print("\n=== 测试查询模式识别 ===")
    
    try:
        test_queries = [
            "最近30天",
            "前10名",
            "涨幅最大",
            "平均市盈率",
            "银行股票"
        ]
        
        for query in test_queries:
            intent = sql_generator._analyze_query_intent(query)
            print(f"查询: '{query}' -> 意图: {intent}")
        
        print(f"\n✅ 查询模式识别测试完成")
        
    except Exception as e:
        print(f"❌ 查询模式测试失败: {e}")

def main():
    """主测试函数"""
    print("开始Text2SQL简化测试...")
    
    # 测试Schema基础功能
    test_schema_basic()
    
    # 测试SQL生成基础功能
    test_sql_generation_basic()
    
    # 测试查询模式识别
    test_query_patterns()
    
    print("\n简化测试完成!")

if __name__ == "__main__":
    main()
