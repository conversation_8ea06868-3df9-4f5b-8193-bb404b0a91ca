"""
测试股票分析功能
"""
from analysis.stock_analyzer import stock_analyzer

def test_stock_analysis():
    """测试股票分析功能"""
    print("=== 测试股票分析功能 ===")
    
    try:
        # 测试综合分析
        print("1. 测试综合分析...")
        analysis_result = stock_analyzer.analyze_stock(
            stock_code="000001.SZ",
            analysis_type="comprehensive",
            include_realtime=False  # 先不包含实时数据
        )
        
        if analysis_result.get("success"):
            print(f"✅ 分析成功!")
            print(f"股票: {analysis_result['stock_code']} - {analysis_result['stock_name']}")
            print(f"分析类型: {analysis_result['analysis_type']}")
            print(f"数据源数量: {analysis_result['sources_count']}")
            print(f"分析结果预览:")
            print(analysis_result['analysis_result'][:500] + "...")
        else:
            print(f"❌ 分析失败: {analysis_result.get('error')}")
        
        # 测试技术分析
        print("\n2. 测试技术分析...")
        tech_analysis = stock_analyzer.analyze_stock(
            stock_code="000001.SZ",
            analysis_type="technical",
            include_realtime=False
        )
        
        if tech_analysis.get("success"):
            print(f"✅ 技术分析成功!")
            print(f"分析结果预览:")
            print(tech_analysis['analysis_result'][:300] + "...")
        else:
            print(f"❌ 技术分析失败: {tech_analysis.get('error')}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_stock_analysis()
