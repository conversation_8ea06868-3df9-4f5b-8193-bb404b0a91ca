"""
模型管理器模块
"""
from typing import Dict, List, Optional, Any
from enum import Enum

from config.settings import settings
from llm.ollama_client import ollama_client
from utils.logger import get_logger

logger = get_logger("model_manager")


class ModelType(Enum):
    """模型类型枚举"""
    TEXT_GENERATION = "text_generation"
    CODE_GENERATION = "code_generation"
    REASONING = "reasoning"
    EMBEDDING = "embedding"


class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        self._model_configs = {
            ModelType.TEXT_GENERATION: {
                "primary": settings.ollama_default_model,
                "fallback": ["qwen2.5:latest", "llama2:latest"]
            },
            ModelType.CODE_GENERATION: {
                "primary": settings.ollama_code_model,
                "fallback": [settings.ollama_default_model]
            },
            ModelType.REASONING: {
                "primary": settings.ollama_reasoning_model,
                "fallback": [settings.ollama_default_model]
            },
            ModelType.EMBEDDING: {
                "primary": settings.ollama_embedding_model,
                "fallback": [settings.ollama_embedding_model_backup, "nomic-embed-text:latest"]
            }
        }
        
        self._model_status = {}
        self._initialized = False
    
    def initialize(self) -> None:
        """初始化模型管理器"""
        if self._initialized:
            return
        
        try:
            # 检查Ollama服务状态
            if not ollama_client.health_check():
                raise ConnectionError("Ollama服务不可用")
            
            # 检查所有模型状态
            self._check_all_models()
            
            self._initialized = True
            logger.info("模型管理器初始化完成")
            
        except Exception as e:
            logger.error(f"模型管理器初始化失败: {e}")
            raise
    
    def _check_all_models(self) -> None:
        """检查所有模型状态"""
        available_models = ollama_client.get_available_models()
        
        for model_type, config in self._model_configs.items():
            primary_model = config["primary"]
            fallback_models = config["fallback"]
            
            # 检查主要模型
            primary_available = primary_model in available_models
            
            # 检查备用模型
            available_fallbacks = [
                model for model in fallback_models 
                if model in available_models
            ]
            
            self._model_status[model_type] = {
                "primary": {
                    "name": primary_model,
                    "available": primary_available
                },
                "fallbacks": [
                    {
                        "name": model,
                        "available": model in available_models
                    }
                    for model in fallback_models
                ],
                "available_fallbacks": available_fallbacks
            }
            
            logger.info(
                f"{model_type.value} - 主要模型: {primary_model} "
                f"({'可用' if primary_available else '不可用'}), "
                f"可用备用模型: {len(available_fallbacks)}"
            )
    
    def get_best_model(self, model_type: ModelType) -> Optional[str]:
        """获取指定类型的最佳可用模型"""
        if not self._initialized:
            self.initialize()
        
        status = self._model_status.get(model_type)
        if not status:
            logger.error(f"未知的模型类型: {model_type}")
            return None
        
        # 优先使用主要模型
        if status["primary"]["available"]:
            return status["primary"]["name"]
        
        # 使用备用模型
        if status["available_fallbacks"]:
            fallback_model = status["available_fallbacks"][0]
            logger.warning(
                f"{model_type.value} 主要模型不可用，使用备用模型: {fallback_model}"
            )
            return fallback_model
        
        logger.error(f"{model_type.value} 没有可用的模型")
        return None
    
    def generate_text(
        self,
        prompt: str,
        model_type: ModelType = ModelType.TEXT_GENERATION,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """生成文本"""
        model = self.get_best_model(model_type)
        if not model:
            raise ValueError(f"没有可用的 {model_type.value} 模型")
        
        try:
            return ollama_client.generate_text(
                prompt=prompt,
                model=model,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            raise
    
    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model_type: ModelType = ModelType.TEXT_GENERATION,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """聊天对话"""
        model = self.get_best_model(model_type)
        if not model:
            raise ValueError(f"没有可用的 {model_type.value} 模型")
        
        try:
            return ollama_client.chat_completion(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            )
        except Exception as e:
            logger.error(f"聊天对话失败: {e}")
            raise
    
    def generate_embeddings(self, text: str) -> List[float]:
        """生成嵌入向量"""
        model = self.get_best_model(ModelType.EMBEDDING)
        if not model:
            raise ValueError("没有可用的嵌入模型")
        
        try:
            return ollama_client.generate_embeddings(text, model)
        except Exception as e:
            logger.error(f"生成嵌入向量失败: {e}")
            raise
    
    def generate_sql(
        self,
        natural_language_query: str,
        schema_info: str,
        examples: Optional[str] = None
    ) -> str:
        """生成SQL查询"""
        system_prompt = f"""
你是一个专业的SQL查询生成助手。根据用户的自然语言描述，生成对应的SQL查询语句。

数据库Schema信息：
{schema_info}

注意事项：
1. 只返回SQL语句，不要包含其他解释
2. 使用标准的MySQL语法
3. 确保查询的安全性，避免SQL注入
4. 优化查询性能
5. 如果查询涉及时间范围，默认查询最近30天的数据
"""
        
        if examples:
            system_prompt += f"\n\n参考示例：\n{examples}"
        
        try:
            return self.generate_text(
                prompt=natural_language_query,
                model_type=ModelType.CODE_GENERATION,
                system_prompt=system_prompt,
                temperature=0.1  # 降低随机性，提高准确性
            )
        except Exception as e:
            logger.error(f"SQL生成失败: {e}")
            raise
    
    def analyze_stock(
        self,
        stock_data: str,
        analysis_type: str = "comprehensive"
    ) -> str:
        """股票分析"""
        system_prompt = f"""
你是一个专业的股票分析师。基于提供的股票数据，进行{analysis_type}分析。

分析要求：
1. 基于数据进行客观分析，不做投资建议
2. 分析技术指标的含义和趋势
3. 评估风险和机会
4. 提供清晰的分析结论
5. 使用中文回答

分析类型：{analysis_type}
"""
        
        try:
            return self.generate_text(
                prompt=stock_data,
                model_type=ModelType.REASONING,
                system_prompt=system_prompt,
                temperature=0.3
            )
        except Exception as e:
            logger.error(f"股票分析失败: {e}")
            raise
    
    def get_model_status(self) -> Dict[str, Any]:
        """获取模型状态信息"""
        if not self._initialized:
            self.initialize()
        
        return {
            "ollama_available": ollama_client.health_check(),
            "available_models": ollama_client.get_available_models(),
            "model_status": {
                model_type.value: status 
                for model_type, status in self._model_status.items()
            }
        }
    
    def refresh_models(self) -> None:
        """刷新模型状态"""
        logger.info("刷新模型状态...")
        ollama_client.get_available_models(force_refresh=True)
        self._check_all_models()
        logger.info("模型状态刷新完成")


# 全局模型管理器实例
model_manager = ModelManager()
