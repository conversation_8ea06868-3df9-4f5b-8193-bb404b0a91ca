"""
Ollama客户端模块
"""
import json
import time
from typing import List, Dict, Any, Optional, Union
import requests
from requests.exceptions import RequestException, Timeout

from config.settings import settings
from utils.logger import get_logger

logger = get_logger("ollama_client")


class OllamaClient:
    """Ollama客户端"""
    
    def __init__(self):
        self.base_url = settings.ollama_base_url.rstrip('/')
        self.timeout = settings.ollama_timeout
        self._available_models = None
        self._last_model_check = 0
        self._model_check_interval = 300  # 5分钟检查一次模型列表
    
    def _make_request(
        self, 
        endpoint: str, 
        method: str = "POST", 
        data: Optional[Dict[str, Any]] = None,
        stream: bool = False
    ) -> Union[Dict[str, Any], requests.Response]:
        """发送HTTP请求到Ollama服务"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, timeout=self.timeout)
            else:
                headers = {"Content-Type": "application/json"}
                response = requests.post(
                    url, 
                    json=data, 
                    headers=headers, 
                    timeout=self.timeout,
                    stream=stream
                )
            
            if stream:
                return response
            
            response.raise_for_status()
            return response.json()
            
        except Timeout:
            logger.error(f"Ollama请求超时: {url}")
            raise
        except RequestException as e:
            logger.error(f"Ollama请求失败: {url}, 错误: {e}")
            raise
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama健康检查失败: {e}")
            return False
    
    def get_available_models(self, force_refresh: bool = False) -> List[str]:
        """获取可用模型列表"""
        current_time = time.time()
        
        # 检查是否需要刷新模型列表
        if (not force_refresh and 
            self._available_models and 
            current_time - self._last_model_check < self._model_check_interval):
            return self._available_models
        
        try:
            response = self._make_request("/api/tags", method="GET")
            models = [model["name"] for model in response.get("models", [])]
            
            self._available_models = models
            self._last_model_check = current_time
            
            logger.info(f"获取到 {len(models)} 个可用模型")
            return models
            
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return self._available_models or []
    
    def is_model_available(self, model_name: str) -> bool:
        """检查模型是否可用"""
        available_models = self.get_available_models()
        return model_name in available_models
    
    def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
        stream: bool = False
    ) -> Union[str, requests.Response]:
        """生成文本"""
        model = model or settings.ollama_default_model

        # 检查模型是否可用
        if not self.is_model_available(model):
            logger.warning(f"模型 {model} 不可用，使用默认模型")
            model = settings.ollama_default_model
        
        # 构建请求数据
        data = {
            "model": model,
            "prompt": prompt,
            "stream": stream,
            "options": {
                "temperature": temperature or settings.ollama_temperature,
                "num_predict": max_tokens or settings.ollama_max_tokens
            }
        }
        
        if system_prompt:
            data["system"] = system_prompt
        
        try:
            if stream:
                return self._make_request("/api/generate", data=data, stream=True)
            else:
                response = self._make_request("/api/generate", data=data)
                return response.get("response", "")
                
        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            raise
    
    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> Union[str, requests.Response]:
        """聊天对话"""
        model = model or settings.ollama_default_model

        # 检查模型是否可用
        if not self.is_model_available(model):
            logger.warning(f"模型 {model} 不可用，使用默认模型")
            model = settings.ollama_default_model
        
        # 构建请求数据
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "options": {
                "temperature": temperature or settings.ollama_temperature,
                "num_predict": max_tokens or settings.ollama_max_tokens
            }
        }
        
        try:
            if stream:
                return self._make_request("/api/chat", data=data, stream=True)
            else:
                response = self._make_request("/api/chat", data=data)
                return response.get("message", {}).get("content", "")
                
        except Exception as e:
            logger.error(f"聊天对话失败: {e}")
            raise
    
    def generate_embeddings(
        self,
        text: Union[str, List[str]],
        model: Optional[str] = None
    ) -> Union[List[float], List[List[float]]]:
        """生成文本嵌入向量"""
        model = model or settings.ollama_embedding_model

        # 检查嵌入模型是否可用
        if not self.is_model_available(model):
            logger.warning(f"嵌入模型 {model} 不可用，尝试备用模型")
            model = settings.ollama_embedding_model_backup
            
            if not self.is_model_available(model):
                raise ValueError("没有可用的嵌入模型")
        
        # 处理单个文本和文本列表
        texts = [text] if isinstance(text, str) else text
        embeddings = []
        
        for single_text in texts:
            data = {
                "model": model,
                "prompt": single_text
            }
            
            try:
                response = self._make_request("/api/embeddings", data=data)
                embedding = response.get("embedding", [])
                embeddings.append(embedding)
                
            except Exception as e:
                logger.error(f"生成嵌入向量失败: {e}")
                raise
        
        # 返回单个向量或向量列表
        return embeddings[0] if isinstance(text, str) else embeddings
    
    def pull_model(self, model_name: str) -> bool:
        """拉取模型"""
        data = {"name": model_name}
        
        try:
            response = self._make_request("/api/pull", data=data, stream=True)
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    try:
                        status = json.loads(line.decode('utf-8'))
                        if status.get("status"):
                            logger.info(f"拉取模型 {model_name}: {status['status']}")
                    except json.JSONDecodeError:
                        continue
            
            # 刷新模型列表
            self.get_available_models(force_refresh=True)
            return True
            
        except Exception as e:
            logger.error(f"拉取模型失败: {e}")
            return False


# 全局Ollama客户端实例
ollama_client = OllamaClient()
