"""
日志工具模块
"""
import os
import sys
from pathlib import Path
from typing import Optional

from loguru import logger

from config.settings import settings


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self._configured = False
    
    def setup_logger(self) -> None:
        """配置日志系统"""
        if self._configured:
            return
        
        # 移除默认处理器
        logger.remove()
        
        # 配置控制台日志
        if settings.log_console_enabled:
            log_format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
            logger.add(
                sys.stdout,
                level=settings.log_level,
                format=log_format,
                colorize=True,
                backtrace=True,
                diagnose=True
            )
        
        # 配置文件日志
        if settings.log_file_enabled:
            # 确保日志目录存在
            log_path = Path(settings.log_file_path)
            log_path.parent.mkdir(parents=True, exist_ok=True)

            log_format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
            logger.add(
                settings.log_file_path,
                level=settings.log_level,
                format=log_format,
                rotation=settings.log_file_rotation,
                retention=settings.log_file_retention,
                compression="zip",
                backtrace=True,
                diagnose=True
            )
        
        self._configured = True
        logger.info("日志系统初始化完成")
    
    def get_logger(self, name: Optional[str] = None):
        """获取日志器实例"""
        if not self._configured:
            self.setup_logger()
        
        if name:
            return logger.bind(name=name)
        return logger


# 全局日志管理器实例
log_manager = LoggerManager()

# 便捷函数
def get_logger(name: Optional[str] = None):
    """获取日志器实例"""
    return log_manager.get_logger(name)

# 默认日志器
app_logger = get_logger("app")
