"""
SQL查询验证器模块
"""
import re
import sqlparse
from typing import Dict, List, Any, Optional
from sqlparse.sql import Statement, Token
from sqlparse.tokens import Keyword, Name

from database.mysql_client import mysql_client
from text2sql.schema_parser import schema_parser
from utils.logger import get_logger

logger = get_logger("query_validator")


class QueryValidator:
    """SQL查询验证器"""
    
    def __init__(self):
        self.forbidden_keywords = [
            "DROP", "DELETE", "INSERT", "UPDATE", "ALTER", "CREATE", 
            "TRUNCATE", "REPLACE", "GRANT", "REVOKE", "EXEC", "EXECUTE"
        ]
        
        self.max_result_limit = 10000  # 最大返回结果数
        self.timeout_seconds = 30      # 查询超时时间
    
    def validate_sql(self, sql: str) -> Dict[str, Any]:
        """验证SQL查询"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": [],
            "estimated_rows": None,
            "complexity": "unknown"
        }
        
        try:
            # 基础安全检查
            security_check = self._check_security(sql)
            if not security_check["is_safe"]:
                validation_result["is_valid"] = False
                validation_result["errors"].extend(security_check["errors"])
                return validation_result
            
            # SQL语法检查
            syntax_check = self._check_syntax(sql)
            if not syntax_check["is_valid"]:
                validation_result["is_valid"] = False
                validation_result["errors"].extend(syntax_check["errors"])
                return validation_result
            
            # 表和字段存在性检查
            schema_check = self._check_schema(sql)
            if not schema_check["is_valid"]:
                validation_result["is_valid"] = False
                validation_result["errors"].extend(schema_check["errors"])
            
            # 性能检查
            performance_check = self._check_performance(sql)
            validation_result["warnings"].extend(performance_check["warnings"])
            validation_result["suggestions"].extend(performance_check["suggestions"])
            validation_result["complexity"] = performance_check["complexity"]
            
            # 估算结果数量
            validation_result["estimated_rows"] = self._estimate_result_count(sql)
            
            logger.info(f"SQL验证完成，有效性: {validation_result['is_valid']}")
            return validation_result
            
        except Exception as e:
            logger.error(f"SQL验证失败: {e}")
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"验证过程出错: {str(e)}")
            return validation_result
    
    def validate_and_execute_dry_run(self, sql: str) -> Dict[str, Any]:
        """验证并执行干运行（EXPLAIN）"""
        try:
            validation_result = self.validate_sql(sql)
            
            if not validation_result["is_valid"]:
                return validation_result
            
            # 执行EXPLAIN查询
            explain_sql = f"EXPLAIN {sql}"
            
            try:
                explain_result = mysql_client.execute_query(explain_sql)
                validation_result["explain_result"] = explain_result
                validation_result["has_explain"] = True
                
                # 分析执行计划
                plan_analysis = self._analyze_execution_plan(explain_result)
                validation_result["plan_analysis"] = plan_analysis
                
            except Exception as e:
                validation_result["warnings"].append(f"无法执行EXPLAIN: {str(e)}")
                validation_result["has_explain"] = False
            
            return validation_result
            
        except Exception as e:
            logger.error(f"干运行验证失败: {e}")
            return {
                "is_valid": False,
                "errors": [f"干运行验证失败: {str(e)}"],
                "warnings": [],
                "suggestions": []
            }
    
    def _check_security(self, sql: str) -> Dict[str, Any]:
        """安全检查"""
        result = {"is_safe": True, "errors": []}
        
        sql_upper = sql.upper()
        
        # 检查禁用关键词
        for keyword in self.forbidden_keywords:
            if keyword in sql_upper:
                result["is_safe"] = False
                result["errors"].append(f"禁止使用关键词: {keyword}")
        
        # 检查是否只包含SELECT语句
        parsed = sqlparse.parse(sql)
        if parsed:
            for statement in parsed:
                if statement.get_type() != "SELECT":
                    result["is_safe"] = False
                    result["errors"].append(f"只允许SELECT查询，发现: {statement.get_type()}")
        
        # 检查SQL注入模式
        injection_patterns = [
            r";\s*(DROP|DELETE|INSERT|UPDATE)",
            r"UNION\s+SELECT",
            r"--\s*$",
            r"/\*.*\*/",
            r"'\s*OR\s*'1'\s*=\s*'1",
            r"'\s*OR\s*1\s*=\s*1"
        ]
        
        for pattern in injection_patterns:
            if re.search(pattern, sql_upper, re.IGNORECASE):
                result["is_safe"] = False
                result["errors"].append("检测到潜在的SQL注入模式")
                break
        
        return result
    
    def _check_syntax(self, sql: str) -> Dict[str, Any]:
        """语法检查"""
        result = {"is_valid": True, "errors": []}
        
        try:
            # 使用sqlparse检查基础语法
            parsed = sqlparse.parse(sql)
            
            if not parsed:
                result["is_valid"] = False
                result["errors"].append("SQL语法解析失败")
                return result
            
            # 检查是否有有效的语句
            valid_statements = [stmt for stmt in parsed if str(stmt).strip()]
            if not valid_statements:
                result["is_valid"] = False
                result["errors"].append("没有有效的SQL语句")
                return result
            
            # 检查括号匹配
            if not self._check_parentheses_balance(sql):
                result["is_valid"] = False
                result["errors"].append("括号不匹配")
            
            # 检查引号匹配
            if not self._check_quotes_balance(sql):
                result["is_valid"] = False
                result["errors"].append("引号不匹配")
            
        except Exception as e:
            result["is_valid"] = False
            result["errors"].append(f"语法检查失败: {str(e)}")
        
        return result
    
    def _check_schema(self, sql: str) -> Dict[str, Any]:
        """Schema检查"""
        result = {"is_valid": True, "errors": []}
        
        try:
            # 提取表名
            tables = self._extract_table_names(sql)
            
            # 验证表是否存在
            for table in tables:
                if not schema_parser.validate_table_exists(table):
                    result["is_valid"] = False
                    result["errors"].append(f"表不存在: {table}")
            
            # 提取字段名（简化版本，只检查明显的字段）
            columns = self._extract_column_names(sql)
            
            # 验证字段是否存在（需要知道对应的表）
            for table in tables:
                table_columns = self._get_table_columns(table)
                for column in columns:
                    if column not in ["*"] and column not in table_columns:
                        # 这里只是警告，因为可能是别名或函数
                        pass
        
        except Exception as e:
            result["errors"].append(f"Schema检查失败: {str(e)}")
        
        return result
    
    def _check_performance(self, sql: str) -> Dict[str, Any]:
        """性能检查"""
        result = {
            "warnings": [],
            "suggestions": [],
            "complexity": "简单"
        }
        
        sql_lower = sql.lower()
        
        # 检查是否有LIMIT子句
        if "limit" not in sql_lower:
            result["warnings"].append("建议添加LIMIT子句限制返回结果数量")
            result["suggestions"].append("在查询末尾添加 LIMIT 100")
        
        # 检查是否有WHERE子句
        if "where" not in sql_lower and "join" not in sql_lower:
            result["warnings"].append("查询没有WHERE条件，可能返回大量数据")
            result["suggestions"].append("添加适当的WHERE条件过滤数据")
        
        # 检查复杂度
        complexity_score = 0
        
        # JOIN数量
        join_count = sql_lower.count("join")
        complexity_score += join_count * 2
        
        # 子查询数量
        subquery_count = sql_lower.count("select") - 1
        complexity_score += subquery_count * 3
        
        # 聚合函数
        agg_functions = ["count", "sum", "avg", "max", "min", "group by"]
        for func in agg_functions:
            if func in sql_lower:
                complexity_score += 1
        
        # 排序
        if "order by" in sql_lower:
            complexity_score += 1
        
        # 确定复杂度等级
        if complexity_score <= 2:
            result["complexity"] = "简单"
        elif complexity_score <= 5:
            result["complexity"] = "中等"
        else:
            result["complexity"] = "复杂"
            result["warnings"].append("查询复杂度较高，可能影响性能")
        
        return result
    
    def _estimate_result_count(self, sql: str) -> Optional[int]:
        """估算结果数量"""
        try:
            # 提取LIMIT值
            limit_match = re.search(r"LIMIT\s+(\d+)", sql, re.IGNORECASE)
            if limit_match:
                return int(limit_match.group(1))
            
            # 如果没有LIMIT，返回估算值
            if "where" in sql.lower():
                return 1000  # 有条件的查询估算
            else:
                return 10000  # 无条件查询估算
                
        except Exception:
            return None
    
    def _analyze_execution_plan(self, explain_result: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析执行计划"""
        analysis = {
            "has_index_scan": False,
            "has_full_table_scan": False,
            "estimated_rows": 0,
            "warnings": []
        }
        
        try:
            for row in explain_result:
                select_type = row.get("select_type", "")
                type_val = row.get("type", "")
                rows = row.get("rows", 0)
                
                # 累计估算行数
                if isinstance(rows, int):
                    analysis["estimated_rows"] += rows
                
                # 检查扫描类型
                if type_val in ["index", "range"]:
                    analysis["has_index_scan"] = True
                elif type_val in ["ALL"]:
                    analysis["has_full_table_scan"] = True
                    analysis["warnings"].append("检测到全表扫描，可能影响性能")
        
        except Exception as e:
            analysis["warnings"].append(f"执行计划分析失败: {str(e)}")
        
        return analysis
    
    def _check_parentheses_balance(self, sql: str) -> bool:
        """检查括号是否平衡"""
        count = 0
        for char in sql:
            if char == '(':
                count += 1
            elif char == ')':
                count -= 1
                if count < 0:
                    return False
        return count == 0
    
    def _check_quotes_balance(self, sql: str) -> bool:
        """检查引号是否平衡"""
        single_quote_count = sql.count("'")
        double_quote_count = sql.count('"')
        
        return single_quote_count % 2 == 0 and double_quote_count % 2 == 0
    
    def _extract_table_names(self, sql: str) -> List[str]:
        """提取表名"""
        tables = []
        
        try:
            # 简化的表名提取（使用正则表达式）
            # 匹配FROM和JOIN后的表名
            patterns = [
                r"FROM\s+(\w+)",
                r"JOIN\s+(\w+)",
                r"UPDATE\s+(\w+)",
                r"INSERT\s+INTO\s+(\w+)"
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, sql, re.IGNORECASE)
                tables.extend(matches)
        
        except Exception as e:
            logger.error(f"提取表名失败: {e}")
        
        return list(set(tables))  # 去重
    
    def _extract_column_names(self, sql: str) -> List[str]:
        """提取字段名（简化版本）"""
        columns = []
        
        try:
            # 提取SELECT后的字段名（简化处理）
            select_match = re.search(r"SELECT\s+(.*?)\s+FROM", sql, re.IGNORECASE | re.DOTALL)
            if select_match:
                select_part = select_match.group(1)
                # 简单分割（不处理复杂的函数和子查询）
                column_parts = select_part.split(",")
                for part in column_parts:
                    part = part.strip()
                    # 提取字段名（去除别名）
                    if " AS " in part.upper():
                        column_name = part.split(" AS ")[0].strip()
                    else:
                        column_name = part.strip()
                    
                    # 去除表前缀
                    if "." in column_name:
                        column_name = column_name.split(".")[-1]
                    
                    columns.append(column_name)
        
        except Exception as e:
            logger.error(f"提取字段名失败: {e}")
        
        return columns
    
    def _get_table_columns(self, table_name: str) -> List[str]:
        """获取表的所有字段名"""
        try:
            table_info = mysql_client.get_table_info(table_name)
            return [col.get("column_name", "") for col in table_info]
        except Exception:
            return []


# 全局查询验证器实例
query_validator = QueryValidator()
