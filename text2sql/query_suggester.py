"""
查询建议器模块
"""
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

from text2sql.schema_parser import schema_parser
from utils.logger import get_logger

logger = get_logger("query_suggester")


class QuerySuggester:
    """查询建议器"""
    
    def __init__(self):
        self.query_templates = self._initialize_templates()
        self.popular_queries = self._initialize_popular_queries()
        self.query_categories = {
            "基础查询": "basic",
            "价格分析": "price",
            "技术指标": "technical", 
            "基本面分析": "fundamental",
            "资金流向": "money_flow",
            "行业分析": "industry",
            "统计分析": "statistics"
        }
    
    def get_suggestions(
        self,
        partial_query: Optional[str] = None,
        category: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """获取查询建议"""
        try:
            suggestions = []
            
            if partial_query:
                # 基于部分查询的建议
                suggestions.extend(self._get_partial_match_suggestions(partial_query))
                
                # 基于关键词的建议
                suggestions.extend(self._get_keyword_suggestions(partial_query))
            
            if category:
                # 基于分类的建议
                suggestions.extend(self._get_category_suggestions(category))
            
            if not suggestions:
                # 默认建议
                suggestions = self._get_default_suggestions()
            
            # 去重和排序
            unique_suggestions = self._deduplicate_suggestions(suggestions)
            
            # 限制数量
            return unique_suggestions[:limit]
            
        except Exception as e:
            logger.error(f"获取查询建议失败: {e}")
            return []
    
    def get_related_suggestions(self, natural_query: str) -> List[Dict[str, str]]:
        """获取相关查询建议"""
        try:
            suggestions = []
            query_lower = natural_query.lower()
            
            # 分析查询意图
            intent_keywords = self._extract_intent_keywords(query_lower)
            
            # 基于意图关键词匹配模板
            for template in self.query_templates:
                template_keywords = template.get("keywords", [])
                
                # 计算关键词匹配度
                match_score = self._calculate_match_score(intent_keywords, template_keywords)
                
                if match_score > 0.3:  # 匹配度阈值
                    suggestions.append({
                        "query": template["query"],
                        "description": template["description"],
                        "category": template["category"],
                        "match_score": match_score
                    })
            
            # 按匹配度排序
            suggestions.sort(key=lambda x: x["match_score"], reverse=True)
            
            return suggestions[:5]  # 返回前5个相关建议
            
        except Exception as e:
            logger.error(f"获取相关建议失败: {e}")
            return []
    
    def search_templates(self, keyword: str) -> List[Dict[str, str]]:
        """搜索查询模板"""
        try:
            results = []
            keyword_lower = keyword.lower()
            
            for template in self.query_templates:
                # 在查询、描述、关键词中搜索
                if (keyword_lower in template["query"].lower() or
                    keyword_lower in template["description"].lower() or
                    any(keyword_lower in kw.lower() for kw in template.get("keywords", []))):
                    
                    results.append({
                        "query": template["query"],
                        "description": template["description"],
                        "category": template["category"]
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"搜索查询模板失败: {e}")
            return []
    
    def get_popular_queries(self) -> List[Dict[str, str]]:
        """获取热门查询"""
        return self.popular_queries
    
    def get_categories(self) -> Dict[str, str]:
        """获取查询分类"""
        return self.query_categories
    
    def add_custom_template(
        self,
        query: str,
        description: str,
        category: str,
        keywords: Optional[List[str]] = None
    ) -> bool:
        """添加自定义查询模板"""
        try:
            template = {
                "query": query,
                "description": description,
                "category": category,
                "keywords": keywords or [],
                "custom": True,
                "created_at": datetime.now().isoformat()
            }
            
            self.query_templates.append(template)
            logger.info(f"添加自定义查询模板: {description}")
            return True
            
        except Exception as e:
            logger.error(f"添加自定义模板失败: {e}")
            return False
    
    def _initialize_templates(self) -> List[Dict[str, Any]]:
        """初始化查询模板"""
        templates = [
            # 基础查询
            {
                "query": "查询平安银行的基本信息",
                "description": "查询特定股票的基本信息",
                "category": "basic",
                "keywords": ["基本信息", "股票", "公司"]
            },
            {
                "query": "查询所有银行股票",
                "description": "按行业查询股票列表",
                "category": "basic",
                "keywords": ["行业", "银行", "列表"]
            },
            
            # 价格分析
            {
                "query": "查询平安银行最近30天的价格走势",
                "description": "查询股票价格历史数据",
                "category": "price",
                "keywords": ["价格", "走势", "历史", "最近"]
            },
            {
                "query": "查询今日涨幅前10的股票",
                "description": "查询涨幅排行榜",
                "category": "price",
                "keywords": ["涨幅", "排行", "前10", "今日"]
            },
            {
                "query": "查询跌幅超过5%的股票",
                "description": "查询大跌股票",
                "category": "price",
                "keywords": ["跌幅", "超过", "大跌"]
            },
            
            # 技术指标
            {
                "query": "查询平安银行的MACD指标",
                "description": "查询股票技术指标",
                "category": "technical",
                "keywords": ["MACD", "技术指标", "技术分析"]
            },
            {
                "query": "查询RSI超过70的股票",
                "description": "查询超买股票",
                "category": "technical",
                "keywords": ["RSI", "超买", "技术指标"]
            },
            
            # 基本面分析
            {
                "query": "查询市盈率低于15的股票",
                "description": "查询低估值股票",
                "category": "fundamental",
                "keywords": ["市盈率", "估值", "低估"]
            },
            {
                "query": "查询市值超过1000亿的股票",
                "description": "查询大盘股",
                "category": "fundamental",
                "keywords": ["市值", "大盘股", "超过"]
            },
            
            # 资金流向
            {
                "query": "查询今日资金净流入前10的股票",
                "description": "查询资金流入排行",
                "category": "money_flow",
                "keywords": ["资金", "净流入", "排行"]
            },
            {
                "query": "查询平安银行的资金流向",
                "description": "查询特定股票资金流向",
                "category": "money_flow",
                "keywords": ["资金流向", "大单", "流入流出"]
            },
            
            # 行业分析
            {
                "query": "查询银行业平均市盈率",
                "description": "查询行业估值水平",
                "category": "industry",
                "keywords": ["行业", "平均", "估值"]
            },
            {
                "query": "比较各行业的平均涨幅",
                "description": "行业表现比较",
                "category": "industry",
                "keywords": ["行业", "比较", "涨幅"]
            },
            
            # 统计分析
            {
                "query": "统计各行业股票数量",
                "description": "行业分布统计",
                "category": "statistics",
                "keywords": ["统计", "数量", "分布"]
            },
            {
                "query": "查询成交量最大的10只股票",
                "description": "成交量排行",
                "category": "statistics",
                "keywords": ["成交量", "最大", "排行"]
            }
        ]
        
        return templates
    
    def _initialize_popular_queries(self) -> List[Dict[str, str]]:
        """初始化热门查询"""
        return [
            {
                "query": "查询今日涨幅前10的股票",
                "description": "今日涨幅榜"
            },
            {
                "query": "查询平安银行的基本信息",
                "description": "股票基本信息查询"
            },
            {
                "query": "查询银行业股票列表",
                "description": "行业股票查询"
            },
            {
                "query": "查询市盈率低于20的股票",
                "description": "低估值股票筛选"
            },
            {
                "query": "查询最近30天成交量最大的股票",
                "description": "活跃股票查询"
            }
        ]
    
    def _get_partial_match_suggestions(self, partial_query: str) -> List[Dict[str, Any]]:
        """基于部分匹配的建议"""
        suggestions = []
        partial_lower = partial_query.lower()
        
        for template in self.query_templates:
            if partial_lower in template["query"].lower():
                suggestions.append({
                    "query": template["query"],
                    "description": template["description"],
                    "category": template["category"],
                    "match_type": "partial"
                })
        
        return suggestions
    
    def _get_keyword_suggestions(self, query: str) -> List[Dict[str, Any]]:
        """基于关键词的建议"""
        suggestions = []
        query_keywords = self._extract_intent_keywords(query.lower())
        
        for template in self.query_templates:
            template_keywords = template.get("keywords", [])
            
            # 检查关键词匹配
            if any(kw in query_keywords for kw in template_keywords):
                suggestions.append({
                    "query": template["query"],
                    "description": template["description"],
                    "category": template["category"],
                    "match_type": "keyword"
                })
        
        return suggestions
    
    def _get_category_suggestions(self, category: str) -> List[Dict[str, Any]]:
        """基于分类的建议"""
        suggestions = []
        
        for template in self.query_templates:
            if template["category"] == category:
                suggestions.append({
                    "query": template["query"],
                    "description": template["description"],
                    "category": template["category"],
                    "match_type": "category"
                })
        
        return suggestions
    
    def _get_default_suggestions(self) -> List[Dict[str, Any]]:
        """默认建议"""
        # 返回热门查询作为默认建议
        suggestions = []
        
        for popular in self.popular_queries:
            suggestions.append({
                "query": popular["query"],
                "description": popular["description"],
                "category": "popular",
                "match_type": "default"
            })
        
        return suggestions
    
    def _extract_intent_keywords(self, query: str) -> List[str]:
        """提取查询意图关键词"""
        keywords = []
        
        # 股票相关关键词
        stock_keywords = ["股票", "公司", "证券", "上市"]
        for kw in stock_keywords:
            if kw in query:
                keywords.append(kw)
        
        # 价格相关关键词
        price_keywords = ["价格", "涨幅", "跌幅", "走势", "行情"]
        for kw in price_keywords:
            if kw in query:
                keywords.append(kw)
        
        # 技术指标关键词
        tech_keywords = ["macd", "kdj", "rsi", "技术", "指标"]
        for kw in tech_keywords:
            if kw in query:
                keywords.append(kw)
        
        # 基本面关键词
        fundamental_keywords = ["市盈率", "市净率", "市值", "估值", "基本面"]
        for kw in fundamental_keywords:
            if kw in query:
                keywords.append(kw)
        
        # 资金关键词
        money_keywords = ["资金", "流入", "流出", "大单", "成交量"]
        for kw in money_keywords:
            if kw in query:
                keywords.append(kw)
        
        # 行业关键词
        industry_keywords = ["行业", "银行", "科技", "医药", "地产"]
        for kw in industry_keywords:
            if kw in query:
                keywords.append(kw)
        
        return keywords
    
    def _calculate_match_score(
        self, 
        query_keywords: List[str], 
        template_keywords: List[str]
    ) -> float:
        """计算匹配分数"""
        if not query_keywords or not template_keywords:
            return 0.0
        
        # 计算交集
        intersection = set(query_keywords) & set(template_keywords)
        
        # 计算Jaccard相似度
        union = set(query_keywords) | set(template_keywords)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)
    
    def _deduplicate_suggestions(
        self, 
        suggestions: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """去重建议"""
        seen_queries = set()
        unique_suggestions = []
        
        for suggestion in suggestions:
            query = suggestion.get("query", "")
            if query not in seen_queries:
                seen_queries.add(query)
                unique_suggestions.append(suggestion)
        
        return unique_suggestions


# 全局查询建议器实例
query_suggester = QuerySuggester()
