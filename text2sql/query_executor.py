"""
查询执行器模块
"""
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd

from database.mysql_client import mysql_client
from text2sql.query_validator import query_validator
from text2sql.result_formatter import result_formatter
from utils.logger import get_logger

logger = get_logger("query_executor")


class QueryExecutor:
    """查询执行器"""
    
    def __init__(self):
        self.max_execution_time = 30  # 最大执行时间（秒）
        self.max_result_rows = 10000   # 最大返回行数
        self.default_limit = 100       # 默认限制行数
    
    def execute_query(
        self,
        sql: str,
        format_type: str = "table",
        include_metadata: bool = True,
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """执行SQL查询"""
        execution_start = time.time()
        
        try:
            logger.info(f"开始执行查询: {sql[:100]}...")
            
            # 验证查询
            validation_result = query_validator.validate_sql(sql)
            
            if not validation_result["is_valid"]:
                return {
                    "success": False,
                    "error": "查询验证失败",
                    "validation_errors": validation_result["errors"],
                    "sql": sql
                }
            
            # 如果是干运行，只返回验证结果
            if dry_run:
                explain_result = query_validator.validate_and_execute_dry_run(sql)
                return {
                    "success": True,
                    "dry_run": True,
                    "validation_result": explain_result,
                    "sql": sql
                }
            
            # 添加默认LIMIT（如果没有）
            modified_sql = self._ensure_limit(sql)
            
            # 执行查询
            execution_result = self._execute_sql_safely(modified_sql)
            
            if not execution_result["success"]:
                return execution_result
            
            raw_data = execution_result["data"]
            execution_time = time.time() - execution_start
            
            # 格式化结果
            formatted_result = result_formatter.format_result(
                data=raw_data,
                format_type=format_type,
                sql=modified_sql
            )
            
            # 构建完整结果
            result = {
                "success": True,
                "data": formatted_result["data"],
                "format_type": format_type,
                "row_count": len(raw_data),
                "execution_time": round(execution_time, 3),
                "sql": modified_sql,
                "original_sql": sql
            }
            
            # 添加元数据
            if include_metadata:
                result["metadata"] = {
                    "columns": formatted_result.get("columns", []),
                    "data_types": formatted_result.get("data_types", {}),
                    "validation_warnings": validation_result.get("warnings", []),
                    "complexity": validation_result.get("complexity", "unknown"),
                    "executed_at": datetime.now().isoformat()
                }
            
            logger.info(f"查询执行成功，返回 {len(raw_data)} 行数据，耗时 {execution_time:.3f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - execution_start
            logger.error(f"查询执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": round(execution_time, 3),
                "sql": sql
            }
    
    def execute_query_to_dataframe(self, sql: str) -> Dict[str, Any]:
        """执行查询并返回DataFrame"""
        try:
            logger.info(f"执行查询到DataFrame: {sql[:100]}...")
            
            # 验证查询
            validation_result = query_validator.validate_sql(sql)
            
            if not validation_result["is_valid"]:
                return {
                    "success": False,
                    "error": "查询验证失败",
                    "validation_errors": validation_result["errors"]
                }
            
            # 添加默认LIMIT
            modified_sql = self._ensure_limit(sql)
            
            # 执行查询
            start_time = time.time()
            df = mysql_client.execute_query_to_df(modified_sql)
            execution_time = time.time() - start_time
            
            return {
                "success": True,
                "dataframe": df,
                "row_count": len(df),
                "column_count": len(df.columns),
                "columns": df.columns.tolist(),
                "execution_time": round(execution_time, 3),
                "sql": modified_sql
            }
            
        except Exception as e:
            logger.error(f"DataFrame查询执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": sql
            }
    
    def execute_batch_queries(
        self,
        queries: List[Dict[str, Any]],
        stop_on_error: bool = False
    ) -> Dict[str, Any]:
        """批量执行查询"""
        try:
            logger.info(f"开始批量执行 {len(queries)} 个查询")
            
            results = []
            total_start_time = time.time()
            
            for i, query_info in enumerate(queries):
                sql = query_info.get("sql", "")
                query_id = query_info.get("id", f"query_{i}")
                format_type = query_info.get("format", "table")
                
                logger.info(f"执行查询 {i+1}/{len(queries)}: {query_id}")
                
                # 执行单个查询
                result = self.execute_query(
                    sql=sql,
                    format_type=format_type,
                    include_metadata=True
                )
                
                result["query_id"] = query_id
                result["query_index"] = i
                results.append(result)
                
                # 如果出错且设置了停止执行
                if not result["success"] and stop_on_error:
                    logger.warning(f"查询 {query_id} 失败，停止批量执行")
                    break
            
            total_execution_time = time.time() - total_start_time
            
            # 统计结果
            successful_count = sum(1 for r in results if r["success"])
            failed_count = len(results) - successful_count
            
            return {
                "success": True,
                "results": results,
                "summary": {
                    "total_queries": len(queries),
                    "executed_queries": len(results),
                    "successful_queries": successful_count,
                    "failed_queries": failed_count,
                    "total_execution_time": round(total_execution_time, 3)
                }
            }
            
        except Exception as e:
            logger.error(f"批量查询执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
    
    def get_query_statistics(self, sql: str) -> Dict[str, Any]:
        """获取查询统计信息"""
        try:
            # 执行EXPLAIN查询
            explain_sql = f"EXPLAIN {sql}"
            explain_result = mysql_client.execute_query(explain_sql)
            
            # 分析统计信息
            stats = {
                "estimated_rows": 0,
                "tables_accessed": [],
                "index_usage": [],
                "scan_types": [],
                "warnings": []
            }
            
            for row in explain_result:
                # 累计估算行数
                rows = row.get("rows", 0)
                if isinstance(rows, int):
                    stats["estimated_rows"] += rows
                
                # 收集表信息
                table = row.get("table")
                if table:
                    stats["tables_accessed"].append(table)
                
                # 收集索引使用信息
                key = row.get("key")
                if key:
                    stats["index_usage"].append({
                        "table": table,
                        "index": key
                    })
                
                # 收集扫描类型
                scan_type = row.get("type")
                if scan_type:
                    stats["scan_types"].append({
                        "table": table,
                        "type": scan_type
                    })
                    
                    # 添加性能警告
                    if scan_type == "ALL":
                        stats["warnings"].append(f"表 {table} 使用全表扫描")
            
            return {
                "success": True,
                "statistics": stats,
                "explain_result": explain_result
            }
            
        except Exception as e:
            logger.error(f"获取查询统计失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _execute_sql_safely(self, sql: str) -> Dict[str, Any]:
        """安全执行SQL"""
        try:
            # 设置查询超时（这里简化处理，实际可能需要数据库层面的超时设置）
            start_time = time.time()
            
            # 执行查询
            data = mysql_client.execute_query(sql)
            
            execution_time = time.time() - start_time
            
            # 检查执行时间
            if execution_time > self.max_execution_time:
                logger.warning(f"查询执行时间过长: {execution_time:.3f}s")
            
            # 检查结果数量
            if len(data) > self.max_result_rows:
                logger.warning(f"查询返回行数过多: {len(data)}")
                # 截断结果
                data = data[:self.max_result_rows]
            
            return {
                "success": True,
                "data": data,
                "execution_time": execution_time,
                "row_count": len(data)
            }
            
        except Exception as e:
            logger.error(f"SQL执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _ensure_limit(self, sql: str) -> str:
        """确保SQL有LIMIT子句"""
        sql_upper = sql.upper()
        
        # 如果已经有LIMIT，直接返回
        if "LIMIT" in sql_upper:
            return sql
        
        # 添加默认LIMIT
        if sql.rstrip().endswith(';'):
            # 移除末尾分号，添加LIMIT，再加分号
            sql_without_semicolon = sql.rstrip()[:-1]
            return f"{sql_without_semicolon} LIMIT {self.default_limit};"
        else:
            return f"{sql} LIMIT {self.default_limit}"
    
    def _estimate_query_cost(self, sql: str) -> Dict[str, Any]:
        """估算查询成本（简化版本）"""
        try:
            # 这里可以实现更复杂的成本估算逻辑
            # 目前只是基于简单的规则
            
            sql_lower = sql.lower()
            cost_score = 0
            
            # JOIN操作增加成本
            join_count = sql_lower.count("join")
            cost_score += join_count * 10
            
            # 子查询增加成本
            subquery_count = sql_lower.count("select") - 1
            cost_score += subquery_count * 15
            
            # 聚合操作增加成本
            agg_functions = ["count", "sum", "avg", "max", "min", "group by"]
            for func in agg_functions:
                if func in sql_lower:
                    cost_score += 5
            
            # 排序增加成本
            if "order by" in sql_lower:
                cost_score += 8
            
            # 没有WHERE条件大幅增加成本
            if "where" not in sql_lower:
                cost_score += 50
            
            # 成本等级
            if cost_score <= 10:
                cost_level = "低"
            elif cost_score <= 30:
                cost_level = "中"
            else:
                cost_level = "高"
            
            return {
                "cost_score": cost_score,
                "cost_level": cost_level,
                "recommendations": self._get_cost_recommendations(cost_score, sql_lower)
            }
            
        except Exception as e:
            logger.error(f"成本估算失败: {e}")
            return {
                "cost_score": 0,
                "cost_level": "未知",
                "recommendations": []
            }
    
    def _get_cost_recommendations(self, cost_score: int, sql_lower: str) -> List[str]:
        """获取成本优化建议"""
        recommendations = []
        
        if cost_score > 30:
            recommendations.append("查询复杂度较高，建议优化")
        
        if "where" not in sql_lower:
            recommendations.append("建议添加WHERE条件过滤数据")
        
        if "limit" not in sql_lower:
            recommendations.append("建议添加LIMIT限制返回结果数量")
        
        if sql_lower.count("join") > 2:
            recommendations.append("多表JOIN可能影响性能，考虑分步查询")
        
        return recommendations


# 全局查询执行器实例
query_executor = QueryExecutor()
