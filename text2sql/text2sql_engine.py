"""
Text2SQL引擎主模块
"""
from typing import Dict, List, Any, Optional
from datetime import datetime

from text2sql.sql_generator import sql_generator
from text2sql.query_executor import query_executor
from text2sql.schema_parser import schema_parser
from text2sql.query_suggester import query_suggester
from utils.logger import get_logger

logger = get_logger("text2sql_engine")


class Text2SQLEngine:
    """Text2SQL引擎"""
    
    def __init__(self):
        self.query_history = []
        self.max_history = 100
    
    def process_natural_query(
        self,
        natural_query: str,
        format_type: str = "table",
        execute: bool = True,
        include_suggestions: bool = True
    ) -> Dict[str, Any]:
        """处理自然语言查询"""
        try:
            logger.info(f"处理自然语言查询: '{natural_query}'")
            
            # 生成SQL
            sql_result = sql_generator.generate_sql(natural_query)
            
            if not sql_result.get("success"):
                return {
                    "success": False,
                    "error": "SQL生成失败",
                    "details": sql_result,
                    "natural_query": natural_query
                }
            
            sql_query = sql_result["sql"]
            
            # 构建基础结果
            result = {
                "success": True,
                "natural_query": natural_query,
                "generated_sql": sql_query,
                "query_intent": sql_result.get("query_intent", {}),
                "complexity": sql_result.get("estimated_complexity", "unknown"),
                "generated_at": datetime.now().isoformat()
            }
            
            # 如果需要执行查询
            if execute:
                execution_result = query_executor.execute_query(
                    sql=sql_query,
                    format_type=format_type,
                    include_metadata=True
                )
                
                result.update({
                    "execution_result": execution_result,
                    "data": execution_result.get("data"),
                    "row_count": execution_result.get("row_count", 0),
                    "execution_time": execution_result.get("execution_time", 0)
                })
                
                if not execution_result.get("success"):
                    result["success"] = False
                    result["error"] = "查询执行失败"
            
            # 添加查询建议
            if include_suggestions:
                suggestions = query_suggester.get_related_suggestions(natural_query)
                result["suggestions"] = suggestions
            
            # 记录查询历史
            self._add_to_history(result)
            
            logger.info(f"自然语言查询处理完成: {result.get('success', False)}")
            return result
            
        except Exception as e:
            logger.error(f"处理自然语言查询失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "natural_query": natural_query
            }
    
    def explain_query(self, natural_query: str) -> Dict[str, Any]:
        """解释查询（不执行）"""
        try:
            logger.info(f"解释查询: '{natural_query}'")
            
            # 生成SQL
            sql_result = sql_generator.generate_sql(natural_query)
            
            if not sql_result.get("success"):
                return {
                    "success": False,
                    "error": "SQL生成失败",
                    "details": sql_result
                }
            
            sql_query = sql_result["sql"]
            
            # 执行干运行
            dry_run_result = query_executor.execute_query(
                sql=sql_query,
                dry_run=True
            )
            
            # 获取查询统计
            stats_result = query_executor.get_query_statistics(sql_query)
            
            return {
                "success": True,
                "natural_query": natural_query,
                "generated_sql": sql_query,
                "query_intent": sql_result.get("query_intent", {}),
                "complexity": sql_result.get("estimated_complexity", "unknown"),
                "dry_run_result": dry_run_result,
                "statistics": stats_result.get("statistics", {}),
                "explain_result": stats_result.get("explain_result", [])
            }
            
        except Exception as e:
            logger.error(f"查询解释失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "natural_query": natural_query
            }
    
    def get_query_suggestions(
        self, 
        partial_query: Optional[str] = None,
        category: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取查询建议"""
        try:
            suggestions = query_suggester.get_suggestions(
                partial_query=partial_query,
                category=category
            )
            
            return {
                "success": True,
                "suggestions": suggestions,
                "partial_query": partial_query,
                "category": category
            }
            
        except Exception as e:
            logger.error(f"获取查询建议失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "suggestions": []
            }
    
    def get_schema_info(self, table_name: Optional[str] = None) -> Dict[str, Any]:
        """获取数据库Schema信息"""
        try:
            if table_name:
                # 获取特定表信息
                table_info = schema_parser.get_table_info(table_name)
                return {
                    "success": True,
                    "table_info": table_info
                }
            else:
                # 获取Schema摘要
                schema_summary = schema_parser.get_schema_summary()
                return {
                    "success": True,
                    "schema_summary": schema_summary
                }
                
        except Exception as e:
            logger.error(f"获取Schema信息失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def validate_sql(self, sql: str) -> Dict[str, Any]:
        """验证SQL查询"""
        try:
            from text2sql.query_validator import query_validator
            
            validation_result = query_validator.validate_and_execute_dry_run(sql)
            
            return {
                "success": True,
                "validation_result": validation_result,
                "sql": sql
            }
            
        except Exception as e:
            logger.error(f"SQL验证失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": sql
            }
    
    def execute_sql_directly(
        self, 
        sql: str, 
        format_type: str = "table"
    ) -> Dict[str, Any]:
        """直接执行SQL查询"""
        try:
            logger.info(f"直接执行SQL: {sql[:100]}...")
            
            execution_result = query_executor.execute_query(
                sql=sql,
                format_type=format_type,
                include_metadata=True
            )
            
            # 记录到历史
            history_entry = {
                "type": "direct_sql",
                "sql": sql,
                "execution_result": execution_result,
                "executed_at": datetime.now().isoformat()
            }
            self._add_to_history(history_entry)
            
            return execution_result
            
        except Exception as e:
            logger.error(f"直接执行SQL失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "sql": sql
            }
    
    def get_query_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取查询历史"""
        return self.query_history[-limit:] if limit > 0 else self.query_history
    
    def clear_history(self) -> None:
        """清空查询历史"""
        self.query_history.clear()
        logger.info("查询历史已清空")
    
    def get_popular_queries(self) -> List[Dict[str, str]]:
        """获取热门查询"""
        return query_suggester.get_popular_queries()
    
    def search_queries(self, keyword: str) -> List[Dict[str, str]]:
        """搜索查询模板"""
        return query_suggester.search_templates(keyword)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 检查各组件状态
            status = {
                "text2sql_engine": "running",
                "sql_generator": "running",
                "query_executor": "running",
                "schema_parser": "running",
                "query_suggester": "running"
            }
            
            # 获取统计信息
            stats = {
                "query_history_count": len(self.query_history),
                "schema_cache_status": "active" if schema_parser._schema_cache else "empty"
            }
            
            return {
                "success": True,
                "status": status,
                "statistics": stats,
                "checked_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _add_to_history(self, query_result: Dict[str, Any]) -> None:
        """添加到查询历史"""
        try:
            # 添加时间戳
            query_result["timestamp"] = datetime.now().isoformat()
            
            # 添加到历史
            self.query_history.append(query_result)
            
            # 限制历史记录数量
            if len(self.query_history) > self.max_history:
                self.query_history = self.query_history[-self.max_history:]
                
        except Exception as e:
            logger.error(f"添加查询历史失败: {e}")


# 全局Text2SQL引擎实例
text2sql_engine = Text2SQLEngine()
