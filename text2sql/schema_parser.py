"""
数据库Schema解析器模块
"""
from typing import Dict, List, Any, Optional
import json

from database.mysql_client import mysql_client
from utils.logger import get_logger

logger = get_logger("schema_parser")


class SchemaParser:
    """数据库Schema解析器"""
    
    def __init__(self):
        self._schema_cache = {}
        self._cache_timestamp = None
        self.cache_ttl = 3600  # 缓存1小时
        
        # 表的中文描述
        self.table_descriptions = {
            "stock_basic": "股票基础信息表，包含股票代码、名称、行业、地区等基本信息",
            "stock_daily_history": "股票日线历史数据表，包含开高低收价格、成交量、成交额等交易数据",
            "stock_factor": "股票技术因子表，包含MACD、KDJ、RSI、布林带等技术指标",
            "stock_daily_basic": "股票日线基本数据表，包含市盈率、市净率、换手率、市值等基本面数据",
            "stock_moneyflow": "股票资金流向表，包含大中小单资金流入流出数据"
        }
        
        # 字段的中文描述
        self.field_descriptions = {
            "ts_code": "股票代码（如：000001.SZ）",
            "symbol": "股票简称代码",
            "name": "股票名称",
            "area": "所属地区",
            "industry": "所属行业",
            "list_date": "上市日期",
            "trade_date": "交易日期",
            "open": "开盘价",
            "high": "最高价",
            "low": "最低价",
            "close": "收盘价",
            "pre_close": "昨收价",
            "change_c": "涨跌额",
            "pct_chg": "涨跌幅（%）",
            "vol": "成交量（手）",
            "amount": "成交额（千元）",
            "turnover_rate": "换手率（%）",
            "turnover_rate_f": "换手率（自由流通股）",
            "volume_ratio": "量比",
            "pe": "市盈率",
            "pe_ttm": "市盈率（TTM）",
            "pb": "市净率",
            "ps": "市销率",
            "ps_ttm": "市销率（TTM）",
            "total_mv": "总市值（万元）",
            "circ_mv": "流通市值（万元）",
            "macd_dif": "MACD DIF值",
            "macd_dea": "MACD DEA值",
            "macd": "MACD值",
            "kdj_k": "KDJ K值",
            "kdj_d": "KDJ D值",
            "kdj_j": "KDJ J值",
            "rsi_6": "RSI 6日",
            "rsi_12": "RSI 12日",
            "rsi_24": "RSI 24日",
            "boll_upper": "布林上轨",
            "boll_mid": "布林中轨",
            "boll_lower": "布林下轨",
            "cci": "CCI指标",
            "buy_lg_amount": "大单买入金额（万元）",
            "sell_lg_amount": "大单卖出金额（万元）",
            "buy_md_amount": "中单买入金额（万元）",
            "sell_md_amount": "中单卖出金额（万元）",
            "buy_sm_amount": "小单买入金额（万元）",
            "sell_sm_amount": "小单卖出金额（万元）",
            "net_mf_amount": "净流入额（万元）"
        }
    
    def get_schema_summary(self, force_refresh: bool = False) -> str:
        """获取数据库Schema摘要"""
        try:
            # 检查缓存
            if not force_refresh and self._schema_cache and self._is_cache_valid():
                return self._schema_cache.get("summary", "")
            
            # 获取所有表信息
            tables_info = self._get_all_tables_info()
            
            # 生成Schema摘要
            summary = self._build_schema_summary(tables_info)
            
            # 更新缓存
            self._update_cache({"summary": summary, "tables_info": tables_info})
            
            return summary
            
        except Exception as e:
            logger.error(f"获取Schema摘要失败: {e}")
            return ""
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取特定表的详细信息"""
        try:
            # 获取表结构
            columns_info = mysql_client.get_table_info(table_name)
            
            # 添加中文描述
            enhanced_columns = []
            for col in columns_info:
                col_name = col.get("column_name", "")
                enhanced_col = {
                    **col,
                    "description": self.field_descriptions.get(col_name, ""),
                    "chinese_name": self._get_chinese_field_name(col_name)
                }
                enhanced_columns.append(enhanced_col)
            
            return {
                "table_name": table_name,
                "description": self.table_descriptions.get(table_name, ""),
                "columns": enhanced_columns,
                "column_count": len(enhanced_columns)
            }
            
        except Exception as e:
            logger.error(f"获取表信息失败 {table_name}: {e}")
            return {}
    
    def get_related_tables(self, keywords: List[str]) -> List[str]:
        """根据关键词获取相关表"""
        related_tables = []
        
        keyword_table_mapping = {
            "股票": ["stock_basic"],
            "基础": ["stock_basic"],
            "基本": ["stock_basic"],
            "价格": ["stock_daily_history"],
            "日线": ["stock_daily_history"],
            "历史": ["stock_daily_history"],
            "交易": ["stock_daily_history"],
            "技术": ["stock_factor"],
            "指标": ["stock_factor"],
            "MACD": ["stock_factor"],
            "KDJ": ["stock_factor"],
            "RSI": ["stock_factor"],
            "布林": ["stock_factor"],
            "基本面": ["stock_daily_basic"],
            "估值": ["stock_daily_basic"],
            "市盈率": ["stock_daily_basic"],
            "市净率": ["stock_daily_basic"],
            "换手": ["stock_daily_basic"],
            "市值": ["stock_daily_basic"],
            "资金": ["stock_moneyflow"],
            "流向": ["stock_moneyflow"],
            "流入": ["stock_moneyflow"],
            "流出": ["stock_moneyflow"]
        }
        
        for keyword in keywords:
            for key, tables in keyword_table_mapping.items():
                if key in keyword:
                    related_tables.extend(tables)
        
        return list(set(related_tables))
    
    def get_field_suggestions(self, partial_field: str) -> List[Dict[str, str]]:
        """获取字段建议"""
        suggestions = []
        partial_lower = partial_field.lower()
        
        for field, description in self.field_descriptions.items():
            if partial_lower in field.lower() or partial_lower in description:
                suggestions.append({
                    "field_name": field,
                    "description": description,
                    "chinese_name": self._get_chinese_field_name(field)
                })
        
        return suggestions[:10]  # 限制返回数量
    
    def validate_table_exists(self, table_name: str) -> bool:
        """验证表是否存在"""
        try:
            all_tables = mysql_client.get_all_tables()
            return table_name in all_tables
        except Exception as e:
            logger.error(f"验证表存在性失败: {e}")
            return False
    
    def validate_column_exists(self, table_name: str, column_name: str) -> bool:
        """验证字段是否存在"""
        try:
            table_info = mysql_client.get_table_info(table_name)
            column_names = [col.get("column_name", "") for col in table_info]
            return column_name in column_names
        except Exception as e:
            logger.error(f"验证字段存在性失败: {e}")
            return False
    
    def _get_all_tables_info(self) -> Dict[str, Any]:
        """获取所有表的信息"""
        tables_info = {}
        
        try:
            all_tables = mysql_client.get_all_tables()
            
            for table_name in all_tables:
                if table_name.startswith("stock_"):  # 只处理股票相关表
                    table_info = self.get_table_info(table_name)
                    if table_info:
                        tables_info[table_name] = table_info
            
            return tables_info
            
        except Exception as e:
            logger.error(f"获取所有表信息失败: {e}")
            return {}
    
    def _build_schema_summary(self, tables_info: Dict[str, Any]) -> str:
        """构建Schema摘要"""
        summary_parts = ["数据库Schema信息：\n"]
        
        for table_name, table_info in tables_info.items():
            summary_parts.append(f"表名：{table_name}")
            summary_parts.append(f"描述：{table_info.get('description', '')}")
            summary_parts.append("主要字段：")
            
            # 选择重要字段显示
            important_fields = self._get_important_fields(table_name)
            columns = table_info.get("columns", [])
            
            for col in columns:
                col_name = col.get("column_name", "")
                if col_name in important_fields or len(important_fields) == 0:
                    data_type = col.get("data_type", "")
                    description = col.get("description", "")
                    summary_parts.append(f"  - {col_name} ({data_type}): {description}")
            
            summary_parts.append("")  # 空行分隔
        
        # 添加表关联信息
        summary_parts.append("表关联关系：")
        summary_parts.append("- 所有表通过 ts_code 字段关联（股票代码）")
        summary_parts.append("- 时间相关查询使用 trade_date 字段")
        summary_parts.append("")
        
        return "\n".join(summary_parts)
    
    def _get_important_fields(self, table_name: str) -> List[str]:
        """获取表的重要字段"""
        important_fields_map = {
            "stock_basic": ["ts_code", "name", "industry", "area", "list_date"],
            "stock_daily_history": ["ts_code", "trade_date", "open", "high", "low", "close", "pct_chg", "vol", "amount"],
            "stock_factor": ["ts_code", "trade_date", "macd", "kdj_k", "kdj_d", "rsi_6", "rsi_12"],
            "stock_daily_basic": ["ts_code", "trade_date", "pe", "pe_ttm", "pb", "turnover_rate", "total_mv"],
            "stock_moneyflow": ["ts_code", "trade_date", "buy_lg_amount", "sell_lg_amount", "net_mf_amount"]
        }
        
        return important_fields_map.get(table_name, [])
    
    def _get_chinese_field_name(self, field_name: str) -> str:
        """获取字段的中文名称"""
        chinese_names = {
            "ts_code": "股票代码",
            "name": "股票名称",
            "industry": "行业",
            "area": "地区",
            "trade_date": "交易日期",
            "close": "收盘价",
            "pct_chg": "涨跌幅",
            "vol": "成交量",
            "amount": "成交额",
            "pe": "市盈率",
            "pb": "市净率",
            "macd": "MACD",
            "kdj_k": "KDJ_K",
            "rsi_6": "RSI6"
        }
        
        return chinese_names.get(field_name, field_name)
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self._cache_timestamp:
            return False
        
        from datetime import datetime
        current_time = datetime.now().timestamp()
        return (current_time - self._cache_timestamp) < self.cache_ttl
    
    def _update_cache(self, data: Dict[str, Any]) -> None:
        """更新缓存"""
        from datetime import datetime
        self._schema_cache = data
        self._cache_timestamp = datetime.now().timestamp()


# 全局Schema解析器实例
schema_parser = SchemaParser()
