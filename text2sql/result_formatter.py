"""
查询结果格式化器模块
"""
import json
import csv
import io
from typing import Dict, List, Any, Optional
from datetime import datetime, date
from decimal import Decimal
import pandas as pd

from utils.logger import get_logger

logger = get_logger("result_formatter")


class ResultFormatter:
    """查询结果格式化器"""
    
    def __init__(self):
        self.supported_formats = [
            "table", "json", "csv", "markdown", "summary", "chart_data"
        ]
    
    def format_result(
        self,
        data: List[Dict[str, Any]],
        format_type: str = "table",
        sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化查询结果"""
        try:
            if format_type not in self.supported_formats:
                format_type = "table"
            
            logger.info(f"格式化查询结果，格式: {format_type}, 数据行数: {len(data)}")
            
            # 预处理数据
            processed_data = self._preprocess_data(data)
            
            # 根据格式类型调用相应的格式化方法
            formatter_map = {
                "table": self._format_as_table,
                "json": self._format_as_json,
                "csv": self._format_as_csv,
                "markdown": self._format_as_markdown,
                "summary": self._format_as_summary,
                "chart_data": self._format_as_chart_data
            }
            
            formatter_func = formatter_map.get(format_type, self._format_as_table)
            formatted_result = formatter_func(processed_data, sql)
            
            # 添加通用元数据
            formatted_result.update({
                "format_type": format_type,
                "row_count": len(data),
                "formatted_at": datetime.now().isoformat()
            })
            
            return formatted_result
            
        except Exception as e:
            logger.error(f"结果格式化失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "format_type": format_type,
                "data": data
            }
    
    def _preprocess_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理数据"""
        processed_data = []
        
        for row in data:
            processed_row = {}
            for key, value in row.items():
                # 处理特殊数据类型
                if isinstance(value, Decimal):
                    processed_row[key] = float(value)
                elif isinstance(value, (date, datetime)):
                    processed_row[key] = value.isoformat()
                elif value is None:
                    processed_row[key] = ""
                else:
                    processed_row[key] = value
            
            processed_data.append(processed_row)
        
        return processed_data
    
    def _format_as_table(
        self, 
        data: List[Dict[str, Any]], 
        sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化为表格"""
        if not data:
            return {
                "data": [],
                "columns": [],
                "data_types": {},
                "display_format": "table"
            }
        
        # 获取列名
        columns = list(data[0].keys())
        
        # 分析数据类型
        data_types = self._analyze_data_types(data, columns)
        
        # 格式化数值显示
        formatted_data = []
        for row in data:
            formatted_row = {}
            for col in columns:
                value = row.get(col, "")
                formatted_row[col] = self._format_cell_value(value, data_types.get(col, "string"))
            formatted_data.append(formatted_row)
        
        return {
            "data": formatted_data,
            "columns": columns,
            "data_types": data_types,
            "display_format": "table"
        }
    
    def _format_as_json(
        self, 
        data: List[Dict[str, Any]], 
        sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化为JSON"""
        return {
            "data": json.dumps(data, ensure_ascii=False, indent=2),
            "raw_data": data,
            "display_format": "json"
        }
    
    def _format_as_csv(
        self, 
        data: List[Dict[str, Any]], 
        sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化为CSV"""
        if not data:
            return {
                "data": "",
                "display_format": "csv"
            }
        
        # 使用StringIO创建CSV
        output = io.StringIO()
        
        # 获取列名
        columns = list(data[0].keys())
        
        # 写入CSV
        writer = csv.DictWriter(output, fieldnames=columns)
        writer.writeheader()
        writer.writerows(data)
        
        csv_content = output.getvalue()
        output.close()
        
        return {
            "data": csv_content,
            "columns": columns,
            "display_format": "csv"
        }
    
    def _format_as_markdown(
        self, 
        data: List[Dict[str, Any]], 
        sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化为Markdown表格"""
        if not data:
            return {
                "data": "| 无数据 |\n|--------|\n",
                "display_format": "markdown"
            }
        
        columns = list(data[0].keys())
        
        # 构建Markdown表格
        markdown_lines = []
        
        # 表头
        header = "| " + " | ".join(columns) + " |"
        markdown_lines.append(header)
        
        # 分隔线
        separator = "| " + " | ".join(["---"] * len(columns)) + " |"
        markdown_lines.append(separator)
        
        # 数据行
        for row in data:
            row_values = []
            for col in columns:
                value = row.get(col, "")
                # 转义Markdown特殊字符
                value_str = str(value).replace("|", "\\|")
                row_values.append(value_str)
            
            row_line = "| " + " | ".join(row_values) + " |"
            markdown_lines.append(row_line)
        
        markdown_content = "\n".join(markdown_lines)
        
        return {
            "data": markdown_content,
            "columns": columns,
            "display_format": "markdown"
        }
    
    def _format_as_summary(
        self, 
        data: List[Dict[str, Any]], 
        sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化为摘要"""
        if not data:
            return {
                "data": "查询结果为空",
                "summary": {
                    "row_count": 0,
                    "column_count": 0
                },
                "display_format": "summary"
            }
        
        columns = list(data[0].keys())
        data_types = self._analyze_data_types(data, columns)
        
        # 生成统计摘要
        summary = {
            "row_count": len(data),
            "column_count": len(columns),
            "columns": columns,
            "data_types": data_types
        }
        
        # 数值列统计
        numeric_stats = {}
        for col in columns:
            if data_types.get(col) in ["number", "decimal"]:
                values = [row.get(col, 0) for row in data if row.get(col) is not None]
                if values:
                    numeric_stats[col] = {
                        "min": min(values),
                        "max": max(values),
                        "avg": sum(values) / len(values),
                        "count": len(values)
                    }
        
        summary["numeric_stats"] = numeric_stats
        
        # 生成文本摘要
        summary_text = f"""
查询结果摘要：
- 总行数：{summary['row_count']}
- 总列数：{summary['column_count']}
- 列名：{', '.join(columns)}
"""
        
        if numeric_stats:
            summary_text += "\n数值列统计：\n"
            for col, stats in numeric_stats.items():
                summary_text += f"- {col}: 最小值={stats['min']:.2f}, 最大值={stats['max']:.2f}, 平均值={stats['avg']:.2f}\n"
        
        return {
            "data": summary_text.strip(),
            "summary": summary,
            "display_format": "summary"
        }
    
    def _format_as_chart_data(
        self, 
        data: List[Dict[str, Any]], 
        sql: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化为图表数据"""
        if not data:
            return {
                "data": {},
                "chart_type": "none",
                "display_format": "chart_data"
            }
        
        columns = list(data[0].keys())
        data_types = self._analyze_data_types(data, columns)
        
        # 分析适合的图表类型
        chart_type = self._suggest_chart_type(data, columns, data_types)
        
        # 准备图表数据
        chart_data = self._prepare_chart_data(data, columns, data_types, chart_type)
        
        return {
            "data": chart_data,
            "chart_type": chart_type,
            "columns": columns,
            "data_types": data_types,
            "display_format": "chart_data"
        }
    
    def _analyze_data_types(
        self, 
        data: List[Dict[str, Any]], 
        columns: List[str]
    ) -> Dict[str, str]:
        """分析数据类型"""
        data_types = {}
        
        for col in columns:
            # 取样本值分析类型
            sample_values = [row.get(col) for row in data[:10] if row.get(col) is not None]
            
            if not sample_values:
                data_types[col] = "string"
                continue
            
            # 判断数据类型
            if all(isinstance(v, (int, float)) for v in sample_values):
                data_types[col] = "number"
            elif all(isinstance(v, str) and v.replace('.', '').replace('-', '').isdigit() for v in sample_values):
                data_types[col] = "decimal"
            elif all(isinstance(v, str) and self._is_date_string(v) for v in sample_values):
                data_types[col] = "date"
            else:
                data_types[col] = "string"
        
        return data_types
    
    def _format_cell_value(self, value: Any, data_type: str) -> str:
        """格式化单元格值"""
        if value is None or value == "":
            return ""
        
        try:
            if data_type == "number":
                if isinstance(value, float):
                    return f"{value:.2f}"
                return str(value)
            elif data_type == "decimal":
                return f"{float(value):.4f}"
            elif data_type == "date":
                return str(value)
            else:
                return str(value)
        except Exception:
            return str(value)
    
    def _is_date_string(self, value: str) -> bool:
        """判断是否为日期字符串"""
        try:
            # 简单的日期格式检查
            date_formats = ["%Y-%m-%d", "%Y/%m/%d", "%Y-%m-%d %H:%M:%S"]
            for fmt in date_formats:
                try:
                    datetime.strptime(value, fmt)
                    return True
                except ValueError:
                    continue
            return False
        except Exception:
            return False
    
    def _suggest_chart_type(
        self, 
        data: List[Dict[str, Any]], 
        columns: List[str], 
        data_types: Dict[str, str]
    ) -> str:
        """建议图表类型"""
        numeric_columns = [col for col, dtype in data_types.items() if dtype in ["number", "decimal"]]
        date_columns = [col for col, dtype in data_types.items() if dtype == "date"]
        string_columns = [col for col, dtype in data_types.items() if dtype == "string"]
        
        # 根据数据特征建议图表类型
        if len(date_columns) >= 1 and len(numeric_columns) >= 1:
            return "line"  # 时间序列图
        elif len(string_columns) >= 1 and len(numeric_columns) >= 1:
            return "bar"   # 柱状图
        elif len(numeric_columns) >= 2:
            return "scatter"  # 散点图
        elif len(string_columns) >= 1:
            return "pie"   # 饼图
        else:
            return "table" # 默认表格
    
    def _prepare_chart_data(
        self, 
        data: List[Dict[str, Any]], 
        columns: List[str], 
        data_types: Dict[str, str], 
        chart_type: str
    ) -> Dict[str, Any]:
        """准备图表数据"""
        chart_data = {
            "labels": [],
            "datasets": [],
            "options": {}
        }
        
        try:
            if chart_type == "line":
                # 时间序列图
                date_col = next((col for col, dtype in data_types.items() if dtype == "date"), None)
                numeric_cols = [col for col, dtype in data_types.items() if dtype in ["number", "decimal"]]
                
                if date_col and numeric_cols:
                    chart_data["labels"] = [row.get(date_col, "") for row in data]
                    
                    for col in numeric_cols[:3]:  # 最多3个数据系列
                        chart_data["datasets"].append({
                            "label": col,
                            "data": [row.get(col, 0) for row in data]
                        })
            
            elif chart_type == "bar":
                # 柱状图
                string_col = next((col for col, dtype in data_types.items() if dtype == "string"), None)
                numeric_col = next((col for col, dtype in data_types.items() if dtype in ["number", "decimal"]), None)
                
                if string_col and numeric_col:
                    chart_data["labels"] = [row.get(string_col, "") for row in data]
                    chart_data["datasets"].append({
                        "label": numeric_col,
                        "data": [row.get(numeric_col, 0) for row in data]
                    })
            
            elif chart_type == "pie":
                # 饼图
                string_col = next((col for col, dtype in data_types.items() if dtype == "string"), None)
                numeric_col = next((col for col, dtype in data_types.items() if dtype in ["number", "decimal"]), None)
                
                if string_col and numeric_col:
                    chart_data["labels"] = [row.get(string_col, "") for row in data]
                    chart_data["datasets"].append({
                        "data": [row.get(numeric_col, 0) for row in data]
                    })
        
        except Exception as e:
            logger.error(f"准备图表数据失败: {e}")
        
        return chart_data


# 全局结果格式化器实例
result_formatter = ResultFormatter()
