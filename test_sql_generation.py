"""
测试SQL生成功能
"""
from text2sql.sql_generator import sql_generator
from text2sql.query_executor import query_executor

def test_sql_generation():
    """测试SQL生成"""
    print("=== 测试SQL生成 ===")
    
    try:
        # 测试简单查询
        natural_query = "查询前5个股票的基本信息"
        print(f"自然语言查询: {natural_query}")
        
        # 生成SQL
        sql_result = sql_generator.generate_sql(natural_query)
        
        if sql_result.get("success"):
            print(f"✅ SQL生成成功!")
            print(f"生成的SQL: {sql_result['sql']}")
            print(f"查询意图: {sql_result.get('query_intent', {})}")
            print(f"复杂度: {sql_result.get('estimated_complexity', 'unknown')}")
            
            # 测试SQL验证
            from text2sql.query_validator import query_validator
            validation = query_validator.validate_sql(sql_result['sql'])
            print(f"SQL验证: {'通过' if validation['is_valid'] else '失败'}")
            
            if validation.get('errors'):
                print(f"验证错误: {validation['errors']}")
            
            # 尝试执行SQL
            if validation['is_valid']:
                print("\n尝试执行SQL...")
                execution_result = query_executor.execute_query(
                    sql=sql_result['sql'],
                    format_type="table",
                    dry_run=False
                )
                
                if execution_result.get("success"):
                    print(f"✅ SQL执行成功!")
                    print(f"返回行数: {execution_result.get('row_count', 0)}")
                    print(f"执行时间: {execution_result.get('execution_time', 0):.3f}s")
                    
                    # 显示前几行数据
                    data = execution_result.get("data", [])
                    if data:
                        print("前3行数据:")
                        for i, row in enumerate(data[:3]):
                            print(f"  行{i+1}: {row}")
                else:
                    print(f"❌ SQL执行失败: {execution_result.get('error')}")
        else:
            print(f"❌ SQL生成失败: {sql_result.get('error')}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_queries():
    """测试多个查询"""
    print("\n=== 测试多个查询 ===")
    
    queries = [
        "查询股票基本信息",
        "查询银行股票",
        "查询前10个股票"
    ]
    
    for i, query in enumerate(queries):
        print(f"\n{i+1}. 测试查询: {query}")
        
        try:
            # 只生成SQL，不执行
            sql_result = sql_generator.generate_sql(query)
            
            if sql_result.get("success"):
                print(f"✅ 生成成功: {sql_result['sql']}")
            else:
                print(f"❌ 生成失败: {sql_result.get('error')}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试SQL生成功能...")
    
    # 测试SQL生成
    test_sql_generation()
    
    # 测试多个查询
    test_multiple_queries()
    
    print("\nSQL生成测试完成!")

if __name__ == "__main__":
    main()
