"""
测试Text2SQL功能
"""
from text2sql.text2sql_engine import text2sql_engine
from text2sql.schema_parser import schema_parser
from text2sql.sql_generator import sql_generator

def test_schema_parser():
    """测试Schema解析器"""
    print("=== 测试Schema解析器 ===")
    
    try:
        # 测试获取Schema摘要
        print("1. 获取Schema摘要...")
        schema_summary = schema_parser.get_schema_summary()
        print(f"Schema摘要长度: {len(schema_summary)}")
        print(f"Schema摘要预览:\n{schema_summary[:500]}...")
        
        # 测试获取表信息
        print("\n2. 获取表信息...")
        table_info = schema_parser.get_table_info("stock_basic")
        print(f"stock_basic表信息: {table_info.get('description', '')}")
        print(f"字段数量: {len(table_info.get('columns', []))}")
        
        # 测试相关表查询
        print("\n3. 测试相关表查询...")
        related_tables = schema_parser.get_related_tables(["价格", "技术指标"])
        print(f"相关表: {related_tables}")
        
    except Exception as e:
        print(f"Schema解析器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_sql_generator():
    """测试SQL生成器"""
    print("\n=== 测试SQL生成器 ===")
    
    try:
        # 测试简单查询
        print("1. 测试简单查询...")
        natural_query = "查询平安银行的基本信息"
        sql_result = sql_generator.generate_sql(natural_query)
        
        if sql_result.get("success"):
            print(f"✅ SQL生成成功!")
            print(f"自然语言: {natural_query}")
            print(f"生成的SQL: {sql_result['sql']}")
            print(f"查询意图: {sql_result.get('query_intent', {})}")
        else:
            print(f"❌ SQL生成失败: {sql_result.get('error')}")
        
        # 测试复杂查询
        print("\n2. 测试复杂查询...")
        complex_query = "查询最近30天涨幅前10的股票"
        complex_result = sql_generator.generate_sql(complex_query)
        
        if complex_result.get("success"):
            print(f"✅ 复杂SQL生成成功!")
            print(f"生成的SQL: {complex_result['sql']}")
        else:
            print(f"❌ 复杂SQL生成失败: {complex_result.get('error')}")
        
    except Exception as e:
        print(f"SQL生成器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_text2sql_engine():
    """测试Text2SQL引擎"""
    print("\n=== 测试Text2SQL引擎 ===")
    
    try:
        # 测试自然语言查询（不执行）
        print("1. 测试自然语言查询解释...")
        natural_query = "查询平安银行的股票代码和名称"
        
        explain_result = text2sql_engine.explain_query(natural_query)
        
        if explain_result.get("success"):
            print(f"✅ 查询解释成功!")
            print(f"自然语言: {natural_query}")
            print(f"生成的SQL: {explain_result['generated_sql']}")
            print(f"复杂度: {explain_result.get('complexity', 'unknown')}")
        else:
            print(f"❌ 查询解释失败: {explain_result.get('error')}")
        
        # 测试查询建议
        print("\n2. 测试查询建议...")
        suggestions = text2sql_engine.get_query_suggestions(
            partial_query="查询股票",
            category="basic"
        )
        
        if suggestions.get("success"):
            print(f"✅ 获取建议成功!")
            print(f"建议数量: {len(suggestions.get('suggestions', []))}")
            for i, suggestion in enumerate(suggestions.get('suggestions', [])[:3]):
                print(f"  {i+1}. {suggestion.get('description', '')}")
        else:
            print(f"❌ 获取建议失败: {suggestions.get('error')}")
        
        # 测试系统状态
        print("\n3. 测试系统状态...")
        status = text2sql_engine.get_system_status()
        
        if status.get("success"):
            print(f"✅ 系统状态正常!")
            print(f"组件状态: {status.get('status', {})}")
            print(f"统计信息: {status.get('statistics', {})}")
        else:
            print(f"❌ 获取系统状态失败: {status.get('error')}")
        
    except Exception as e:
        print(f"Text2SQL引擎测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_simple_execution():
    """测试简单执行"""
    print("\n=== 测试简单执行 ===")
    
    try:
        # 测试一个简单的查询执行
        print("1. 测试简单查询执行...")
        natural_query = "查询前5个股票的基本信息"
        
        result = text2sql_engine.process_natural_query(
            natural_query=natural_query,
            format_type="table",
            execute=True
        )
        
        if result.get("success"):
            print(f"✅ 查询执行成功!")
            print(f"自然语言: {natural_query}")
            print(f"生成的SQL: {result['generated_sql']}")
            
            execution_result = result.get("execution_result", {})
            if execution_result.get("success"):
                print(f"返回行数: {result.get('row_count', 0)}")
                print(f"执行时间: {result.get('execution_time', 0):.3f}s")
                
                # 显示前几行数据
                data = result.get("data", [])
                if data:
                    print("前3行数据:")
                    for i, row in enumerate(data[:3]):
                        print(f"  行{i+1}: {row}")
            else:
                print(f"❌ 查询执行失败: {execution_result.get('error')}")
        else:
            print(f"❌ 处理失败: {result.get('error')}")
        
    except Exception as e:
        print(f"简单执行测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试Text2SQL系统...")
    
    # 测试Schema解析器
    test_schema_parser()
    
    # 测试SQL生成器
    test_sql_generator()
    
    # 测试Text2SQL引擎
    test_text2sql_engine()
    
    # 测试简单执行
    test_simple_execution()
    
    print("\nText2SQL系统测试完成!")

if __name__ == "__main__":
    main()
