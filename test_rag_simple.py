"""
简化的RAG测试
"""
from rag.retriever import rag_retriever

def test_simple_search():
    """测试简单搜索"""
    print("=== 测试简单搜索 ===")
    
    try:
        # 测试基础检索（不使用过滤条件）
        print("1. 测试基础检索...")
        results = rag_retriever.search_relevant_context("平安银行", top_k=5)
        print(f"检索结果数量: {len(results)}")
        
        if results:
            for i, result in enumerate(results):
                print(f"结果 {i+1}:")
                print(f"  ID: {result['id']}")
                print(f"  相似度: {result.get('similarity_score', 'N/A')}")
                print(f"  内容预览: {result['content'][:100]}...")
                print(f"  元数据: {result['metadata']}")
                print()
        
        # 测试单条件过滤
        print("2. 测试单条件过滤...")
        filtered_results = rag_retriever.search_relevant_context(
            query="股票",
            filters={"ts_code": "000001.SZ"},
            top_k=3
        )
        print(f"过滤检索结果数量: {len(filtered_results)}")
        
        if filtered_results:
            print("第一个过滤结果:")
            print(f"  ID: {filtered_results[0]['id']}")
            print(f"  内容预览: {filtered_results[0]['content'][:100]}...")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_search()
