"""
调试相似度分数
"""
from database.vector_client import vector_client
from llm.model_manager import model_manager

def debug_similarity():
    """调试相似度分数"""
    print("=== 调试相似度分数 ===")
    
    try:
        # 初始化
        model_manager.initialize()
        vector_client.initialize()
        
        # 生成查询向量
        query = "平安银行"
        query_embedding = model_manager.generate_embeddings(query)
        print(f"查询: {query}")
        print(f"查询向量维度: {len(query_embedding)}")
        
        # 执行搜索
        results = vector_client.query_documents(
            query_embeddings=[query_embedding],
            n_results=5
        )
        
        print(f"返回结果数量: {len(results.get('ids', [[]])[0])}")
        
        if results and results.get("ids") and results["ids"][0]:
            for i, doc_id in enumerate(results["ids"][0]):
                distance = results["distances"][0][i] if results.get("distances") else float('inf')

                # 使用新的相似度计算方法
                if distance == 0:
                    similarity = 1.0
                else:
                    similarity = 1.0 / (1.0 + distance / 100.0)

                content = results["documents"][0][i] if results.get("documents") else ""
                metadata = results["metadatas"][0][i] if results.get("metadatas") else {}

                print(f"\n结果 {i+1}:")
                print(f"  ID: {doc_id}")
                print(f"  距离: {distance:.4f}")
                print(f"  相似度: {similarity:.4f}")
                print(f"  内容预览: {content[:100]}...")
                print(f"  元数据: {metadata}")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_similarity()
