# 环境配置文件

# 基础环境
ENVIRONMENT=development
DEBUG=true

# MySQL数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_DATABASE=stock_cursor
MYSQL_CHARSET=utf8mb4

# MySQL连接池配置
MYSQL_POOL_SIZE=10
MYSQL_MAX_OVERFLOW=20
MYSQL_POOL_TIMEOUT=30

# ChromaDB配置
CHROMA_PERSIST_DIRECTORY=./data/chroma_db
CHROMA_COLLECTION_NAME=stock_vectors

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_DEFAULT_MODEL=qwen2.5:latest
OLLAMA_CODE_MODEL=qwen2.5-coder:latest
OLLAMA_REASONING_MODEL=deepseek-r1:latest
OLLAMA_EMBEDDING_MODEL=bge-large:latest
OLLAMA_EMBEDDING_MODEL_BACKUP=bge-m3:latest
OLLAMA_TEMPERATURE=0.7
OLLAMA_MAX_TOKENS=2048
OLLAMA_TIMEOUT=60

# RAG配置
RAG_TOP_K=10
RAG_SIMILARITY_THRESHOLD=0.7
RAG_MAX_CONTEXT_LENGTH=4000
RAG_CHUNK_SIZE=500
RAG_CHUNK_OVERLAP=50
RAG_ENABLE_CACHE=true
RAG_CACHE_TTL=3600

# API配置
API_TITLE=股票分析RAG系统
API_DESCRIPTION=基于RAG+大模型的股票分析系统
API_VERSION=1.0.0
API_HOST=0.0.0.0
API_PORT=8000


# 日志配置
LOG_LEVEL=INFO
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log
LOG_FILE_ROTATION=1 day
LOG_FILE_RETENTION=30 days
LOG_CONSOLE_ENABLED=true
