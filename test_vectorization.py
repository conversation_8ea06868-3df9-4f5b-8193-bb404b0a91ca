"""
测试向量化功能
"""
from vectorization.vector_manager import vector_manager

def test_vectorization():
    """测试向量化功能"""
    try:
        print("开始测试向量化...")
        
        # 测试向量化一只股票
        success = vector_manager.add_stock_to_vector_db('000001.SZ', days=5)
        print(f'向量化结果: {success}')
        
        if success:
            # 测试搜索
            results = vector_manager.search_similar_stocks("平安银行", n_results=5)
            print(f'搜索结果数量: {len(results)}')
            
            for result in results[:2]:
                print(f"ID: {result['id']}")
                print(f"内容: {result['content'][:100]}...")
                print(f"元数据: {result['metadata']}")
                print("---")
        
        # 获取统计信息
        stats = vector_manager.get_vector_statistics()
        print(f'向量统计: {stats}')
        
    except Exception as e:
        print(f'测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_vectorization()
