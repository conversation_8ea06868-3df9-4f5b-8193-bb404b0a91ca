"""
测试RAG检索和分析功能
"""
from rag.retriever import rag_retriever
from rag.context_builder import context_builder
from analysis.stock_analyzer import stock_analyzer

def test_rag_retrieval():
    """测试RAG检索功能"""
    print("=== 测试RAG检索功能 ===")
    
    try:
        # 测试基础检索
        print("1. 测试基础检索...")
        results = rag_retriever.search_relevant_context("平安银行股票分析", top_k=5)
        print(f"检索结果数量: {len(results)}")
        
        if results:
            print("第一个结果:")
            print(f"ID: {results[0]['id']}")
            print(f"相似度: {results[0].get('similarity_score', 'N/A')}")
            print(f"内容预览: {results[0]['content'][:100]}...")
            print(f"元数据: {results[0]['metadata']}")
        
        # 测试股票特定检索
        print("\n2. 测试股票特定检索...")
        stock_results = rag_retriever.search_stock_context("000001.SZ", top_k=3)
        print(f"股票检索结果数量: {len(stock_results)}")
        
        # 测试上下文摘要
        print("\n3. 测试上下文摘要...")
        summary = rag_retriever.get_context_summary(results)
        print(f"摘要信息: {summary}")
        
    except Exception as e:
        print(f"RAG检索测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_context_builder():
    """测试上下文构建功能"""
    print("\n=== 测试上下文构建功能 ===")
    
    try:
        # 测试股票分析上下文
        print("1. 测试股票分析上下文...")
        context_data = context_builder.build_context(
            query="分析平安银行的投资价值",
            context_type="stock_analysis",
            stock_codes=["000001.SZ"]
        )
        
        print(f"上下文长度: {len(context_data.get('context_text', ''))}")
        print(f"数据源数量: {len(context_data.get('sources', []))}")
        print(f"摘要: {context_data.get('summary', {})}")
        
        # 测试技术分析上下文
        print("\n2. 测试技术分析上下文...")
        tech_context = context_builder.build_context(
            query="技术指标分析",
            context_type="technical_analysis",
            stock_codes=["000001.SZ"]
        )
        
        print(f"技术分析上下文长度: {len(tech_context.get('context_text', ''))}")
        
    except Exception as e:
        print(f"上下文构建测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_stock_analysis():
    """测试股票分析功能"""
    print("\n=== 测试股票分析功能 ===")
    
    try:
        # 测试综合分析
        print("1. 测试综合分析...")
        analysis_result = stock_analyzer.analyze_stock(
            stock_code="000001.SZ",
            analysis_type="comprehensive",
            include_realtime=True
        )
        
        if analysis_result.get("success"):
            print(f"分析成功!")
            print(f"股票: {analysis_result['stock_code']} - {analysis_result['stock_name']}")
            print(f"分析类型: {analysis_result['analysis_type']}")
            print(f"数据源数量: {analysis_result['sources_count']}")
            print(f"分析结果预览: {analysis_result['analysis_result'][:200]}...")
        else:
            print(f"分析失败: {analysis_result.get('error')}")
        
        # 测试技术分析
        print("\n2. 测试技术分析...")
        tech_analysis = stock_analyzer.analyze_stock(
            stock_code="000001.SZ",
            analysis_type="technical",
            include_realtime=False
        )
        
        if tech_analysis.get("success"):
            print(f"技术分析成功!")
            print(f"分析结果预览: {tech_analysis['analysis_result'][:200]}...")
        else:
            print(f"技术分析失败: {tech_analysis.get('error')}")
        
    except Exception as e:
        print(f"股票分析测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试RAG检索和分析系统...")
    
    # 测试RAG检索
    test_rag_retrieval()
    
    # 测试上下文构建
    test_context_builder()
    
    # 测试股票分析
    test_stock_analysis()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
