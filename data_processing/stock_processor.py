"""
股票数据处理模块
"""
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np
from decimal import Decimal

from database.mysql_client import mysql_client
from database.models import StockBasic, StockDailyHistory, StockFactor
from utils.logger import get_logger

logger = get_logger("stock_processor")


class StockDataProcessor:
    """股票数据处理器"""
    
    def __init__(self):
        self._stock_cache = {}
        self._cache_ttl = 3600  # 缓存1小时
        self._last_cache_time = {}
    
    def get_stock_basic_info(self, ts_code: str) -> Optional[Dict[str, Any]]:
        """获取股票基础信息"""
        try:
            query = """
            SELECT ts_code, symbol, name, area, industry, list_date
            FROM stock_basic 
            WHERE ts_code = :ts_code
            """
            
            result = mysql_client.execute_query(query, {"ts_code": ts_code})
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"获取股票基础信息失败 {ts_code}: {e}")
            return None
    
    def get_stock_daily_data(
        self, 
        ts_code: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取股票日线数据"""
        try:
            # 构建查询条件
            where_conditions = ["ts_code = :ts_code"]
            params = {"ts_code": ts_code}
            
            if start_date:
                where_conditions.append("trade_date >= :start_date")
                params["start_date"] = start_date
            
            if end_date:
                where_conditions.append("trade_date <= :end_date")
                params["end_date"] = end_date
            
            query = f"""
            SELECT ts_code, trade_date, open, high, low, close, 
                   pre_close, change_c, pct_chg, vol, amount
            FROM stock_daily_history 
            WHERE {' AND '.join(where_conditions)}
            ORDER BY trade_date DESC
            LIMIT :limit
            """
            
            params["limit"] = limit
            return mysql_client.execute_query(query, params)
            
        except Exception as e:
            logger.error(f"获取股票日线数据失败 {ts_code}: {e}")
            return []
    
    def get_stock_technical_data(
        self, 
        ts_code: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取股票技术指标数据"""
        try:
            where_conditions = ["ts_code = :ts_code"]
            params = {"ts_code": ts_code}
            
            if start_date:
                where_conditions.append("trade_date >= :start_date")
                params["start_date"] = start_date
            
            if end_date:
                where_conditions.append("trade_date <= :end_date")
                params["end_date"] = end_date
            
            query = f"""
            SELECT ts_code, trade_date, close,
                   macd_dif, macd_dea, macd,
                   kdj_k, kdj_d, kdj_j,
                   rsi_6, rsi_12, rsi_24,
                   boll_upper, boll_mid, boll_lower,
                   cci
            FROM stock_factor 
            WHERE {' AND '.join(where_conditions)}
            ORDER BY trade_date DESC
            LIMIT :limit
            """
            
            params["limit"] = limit
            return mysql_client.execute_query(query, params)
            
        except Exception as e:
            logger.error(f"获取股票技术指标数据失败 {ts_code}: {e}")
            return []
    
    def get_stock_fundamental_data(
        self, 
        ts_code: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取股票基本面数据"""
        try:
            where_conditions = ["ts_code = :ts_code"]
            params = {"ts_code": ts_code}
            
            if start_date:
                where_conditions.append("trade_date >= :start_date")
                params["start_date"] = start_date
            
            if end_date:
                where_conditions.append("trade_date <= :end_date")
                params["end_date"] = end_date
            
            query = f"""
            SELECT ts_code, trade_date, close, turnover_rate, turnover_rate_f,
                   volume_ratio, pe, pe_ttm, pb, ps, ps_ttm,
                   total_mv, circ_mv
            FROM stock_daily_basic 
            WHERE {' AND '.join(where_conditions)}
            ORDER BY trade_date DESC
            LIMIT :limit
            """
            
            params["limit"] = limit
            return mysql_client.execute_query(query, params)
            
        except Exception as e:
            logger.error(f"获取股票基本面数据失败 {ts_code}: {e}")
            return []
    
    def get_stock_money_flow_data(
        self, 
        ts_code: str, 
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """获取股票资金流向数据"""
        try:
            where_conditions = ["ts_code = :ts_code"]
            params = {"ts_code": ts_code}
            
            if start_date:
                where_conditions.append("trade_date >= :start_date")
                params["start_date"] = start_date
            
            if end_date:
                where_conditions.append("trade_date <= :end_date")
                params["end_date"] = end_date
            
            query = f"""
            SELECT ts_code, trade_date,
                   buy_lg_amount, sell_lg_amount,
                   buy_md_amount, sell_md_amount,
                   buy_sm_amount, sell_sm_amount,
                   net_mf_amount
            FROM stock_moneyflow 
            WHERE {' AND '.join(where_conditions)}
            ORDER BY trade_date DESC
            LIMIT :limit
            """
            
            params["limit"] = limit
            return mysql_client.execute_query(query, params)
            
        except Exception as e:
            logger.error(f"获取股票资金流向数据失败 {ts_code}: {e}")
            return []
    
    def get_comprehensive_stock_data(
        self, 
        ts_code: str, 
        days: int = 30
    ) -> Dict[str, Any]:
        """获取股票综合数据"""
        try:
            # 计算日期范围
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            # 获取基础信息
            basic_info = self.get_stock_basic_info(ts_code)
            if not basic_info:
                logger.warning(f"未找到股票基础信息: {ts_code}")
                return {}
            
            # 获取各类数据
            daily_data = self.get_stock_daily_data(ts_code, start_date, end_date, days)
            technical_data = self.get_stock_technical_data(ts_code, start_date, end_date, days)
            fundamental_data = self.get_stock_fundamental_data(ts_code, start_date, end_date, days)
            money_flow_data = self.get_stock_money_flow_data(ts_code, start_date, end_date, days)
            
            # 组装综合数据
            comprehensive_data = {
                "basic_info": basic_info,
                "daily_data": daily_data,
                "technical_data": technical_data,
                "fundamental_data": fundamental_data,
                "money_flow_data": money_flow_data,
                "data_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "summary": {
                    "daily_records": len(daily_data),
                    "technical_records": len(technical_data),
                    "fundamental_records": len(fundamental_data),
                    "money_flow_records": len(money_flow_data)
                }
            }
            
            logger.info(f"获取股票综合数据成功 {ts_code}: {comprehensive_data['summary']}")
            return comprehensive_data
            
        except Exception as e:
            logger.error(f"获取股票综合数据失败 {ts_code}: {e}")
            return {}
    
    def format_stock_data_for_analysis(self, stock_data: Dict[str, Any]) -> str:
        """格式化股票数据用于分析"""
        try:
            if not stock_data:
                return ""
            
            basic_info = stock_data.get("basic_info", {})
            daily_data = stock_data.get("daily_data", [])
            technical_data = stock_data.get("technical_data", [])
            fundamental_data = stock_data.get("fundamental_data", [])
            money_flow_data = stock_data.get("money_flow_data", [])
            
            # 构建分析文本
            analysis_text = []
            
            # 基础信息
            if basic_info:
                analysis_text.append(f"股票基础信息：")
                analysis_text.append(f"股票代码：{basic_info.get('ts_code', 'N/A')}")
                analysis_text.append(f"股票名称：{basic_info.get('name', 'N/A')}")
                analysis_text.append(f"所属行业：{basic_info.get('industry', 'N/A')}")
                analysis_text.append(f"所属地区：{basic_info.get('area', 'N/A')}")
                analysis_text.append("")
            
            # 最新价格数据
            if daily_data:
                latest_daily = daily_data[0]
                analysis_text.append(f"最新价格数据（{latest_daily.get('trade_date', 'N/A')}）：")
                analysis_text.append(f"收盘价：{latest_daily.get('close', 'N/A')}")
                analysis_text.append(f"涨跌幅：{latest_daily.get('pct_chg', 'N/A')}%")
                analysis_text.append(f"成交量：{latest_daily.get('vol', 'N/A')}手")
                analysis_text.append(f"成交额：{latest_daily.get('amount', 'N/A')}千元")
                analysis_text.append("")
            
            # 技术指标
            if technical_data:
                latest_tech = technical_data[0]
                analysis_text.append(f"最新技术指标：")
                analysis_text.append(f"MACD：DIF={latest_tech.get('macd_dif', 'N/A')}, DEA={latest_tech.get('macd_dea', 'N/A')}, MACD={latest_tech.get('macd', 'N/A')}")
                analysis_text.append(f"KDJ：K={latest_tech.get('kdj_k', 'N/A')}, D={latest_tech.get('kdj_d', 'N/A')}, J={latest_tech.get('kdj_j', 'N/A')}")
                analysis_text.append(f"RSI：RSI6={latest_tech.get('rsi_6', 'N/A')}, RSI12={latest_tech.get('rsi_12', 'N/A')}, RSI24={latest_tech.get('rsi_24', 'N/A')}")
                analysis_text.append(f"布林带：上轨={latest_tech.get('boll_upper', 'N/A')}, 中轨={latest_tech.get('boll_mid', 'N/A')}, 下轨={latest_tech.get('boll_lower', 'N/A')}")
                analysis_text.append("")
            
            # 基本面数据
            if fundamental_data:
                latest_fund = fundamental_data[0]
                analysis_text.append(f"最新基本面数据：")
                analysis_text.append(f"市盈率：PE={latest_fund.get('pe', 'N/A')}, PE_TTM={latest_fund.get('pe_ttm', 'N/A')}")
                analysis_text.append(f"市净率：{latest_fund.get('pb', 'N/A')}")
                analysis_text.append(f"换手率：{latest_fund.get('turnover_rate', 'N/A')}%")
                analysis_text.append(f"总市值：{latest_fund.get('total_mv', 'N/A')}万元")
                analysis_text.append("")
            
            # 资金流向
            if money_flow_data:
                latest_money = money_flow_data[0]
                analysis_text.append(f"最新资金流向：")
                analysis_text.append(f"净流入：{latest_money.get('net_mf_amount', 'N/A')}万元")
                analysis_text.append(f"大单净流入：{(latest_money.get('buy_lg_amount', 0) or 0) - (latest_money.get('sell_lg_amount', 0) or 0)}万元")
                analysis_text.append("")
            
            return "\n".join(analysis_text)
            
        except Exception as e:
            logger.error(f"格式化股票数据失败: {e}")
            return ""


# 全局股票数据处理器实例
stock_processor = StockDataProcessor()
