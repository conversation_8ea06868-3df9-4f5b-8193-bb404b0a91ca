"""
数据清洗模块
"""
from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np
from datetime import datetime, date
from decimal import Decimal

from utils.logger import get_logger

logger = get_logger("data_cleaner")


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.numeric_fields = {
            'open', 'high', 'low', 'close', 'pre_close', 'change_c', 'pct_chg',
            'vol', 'amount', 'turnover_rate', 'turnover_rate_f', 'volume_ratio',
            'pe', 'pe_ttm', 'pb', 'ps', 'ps_ttm', 'total_mv', 'circ_mv',
            'macd_dif', 'macd_dea', 'macd', 'kdj_k', 'kdj_d', 'kdj_j',
            'rsi_6', 'rsi_12', 'rsi_24', 'boll_upper', 'boll_mid', 'boll_lower',
            'cci', 'buy_lg_amount', 'sell_lg_amount', 'buy_md_amount', 'sell_md_amount',
            'buy_sm_amount', 'sell_sm_amount', 'net_mf_amount'
        }
        
        self.date_fields = {'trade_date', 'list_date'}
    
    def clean_numeric_value(self, value: Any) -> Optional[float]:
        """清洗数值类型数据"""
        if value is None:
            return None
        
        try:
            # 处理Decimal类型
            if isinstance(value, Decimal):
                return float(value)
            
            # 处理字符串
            if isinstance(value, str):
                value = value.strip()
                if value == '' or value.lower() in ['null', 'none', 'nan']:
                    return None
                return float(value)
            
            # 处理数值类型
            if isinstance(value, (int, float)):
                if np.isnan(value) or np.isinf(value):
                    return None
                return float(value)
            
            return None
            
        except (ValueError, TypeError):
            logger.warning(f"无法转换数值: {value}")
            return None
    
    def clean_date_value(self, value: Any) -> Optional[date]:
        """清洗日期类型数据"""
        if value is None:
            return None
        
        try:
            # 如果已经是date类型
            if isinstance(value, date):
                return value
            
            # 如果是datetime类型
            if isinstance(value, datetime):
                return value.date()
            
            # 如果是字符串
            if isinstance(value, str):
                value = value.strip()
                if value == '' or value.lower() in ['null', 'none']:
                    return None
                
                # 尝试不同的日期格式
                date_formats = ['%Y-%m-%d', '%Y%m%d', '%Y/%m/%d']
                for fmt in date_formats:
                    try:
                        return datetime.strptime(value, fmt).date()
                    except ValueError:
                        continue
            
            return None
            
        except (ValueError, TypeError):
            logger.warning(f"无法转换日期: {value}")
            return None
    
    def clean_string_value(self, value: Any) -> Optional[str]:
        """清洗字符串类型数据"""
        if value is None:
            return None
        
        try:
            value_str = str(value).strip()
            if value_str == '' or value_str.lower() in ['null', 'none', 'nan']:
                return None
            return value_str
            
        except Exception:
            logger.warning(f"无法转换字符串: {value}")
            return None
    
    def clean_record(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """清洗单条记录"""
        cleaned_record = {}
        
        for key, value in record.items():
            if key in self.numeric_fields:
                cleaned_record[key] = self.clean_numeric_value(value)
            elif key in self.date_fields:
                cleaned_record[key] = self.clean_date_value(value)
            else:
                cleaned_record[key] = self.clean_string_value(value)
        
        return cleaned_record
    
    def clean_records(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """清洗记录列表"""
        cleaned_records = []
        
        for record in records:
            try:
                cleaned_record = self.clean_record(record)
                cleaned_records.append(cleaned_record)
            except Exception as e:
                logger.error(f"清洗记录失败: {record}, 错误: {e}")
                continue
        
        logger.info(f"数据清洗完成，原始记录: {len(records)}, 清洗后记录: {len(cleaned_records)}")
        return cleaned_records
    
    def validate_stock_data(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证股票数据的完整性和合理性"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # 检查基础信息
            basic_info = stock_data.get("basic_info")
            if not basic_info:
                validation_result["errors"].append("缺少股票基础信息")
                validation_result["is_valid"] = False
            else:
                if not basic_info.get("ts_code"):
                    validation_result["errors"].append("缺少股票代码")
                    validation_result["is_valid"] = False
                
                if not basic_info.get("name"):
                    validation_result["warnings"].append("缺少股票名称")
            
            # 检查日线数据
            daily_data = stock_data.get("daily_data", [])
            if not daily_data:
                validation_result["warnings"].append("缺少日线数据")
            else:
                # 检查价格数据的合理性
                for record in daily_data[:5]:  # 检查最近5条记录
                    close = record.get("close")
                    high = record.get("high")
                    low = record.get("low")
                    
                    if close and high and low:
                        if close > high or close < low:
                            validation_result["warnings"].append(
                                f"价格数据异常: {record.get('trade_date')} 收盘价不在最高最低价范围内"
                            )
                        
                        if high < low:
                            validation_result["errors"].append(
                                f"价格数据错误: {record.get('trade_date')} 最高价小于最低价"
                            )
                            validation_result["is_valid"] = False
            
            # 检查技术指标数据
            technical_data = stock_data.get("technical_data", [])
            if technical_data:
                for record in technical_data[:3]:  # 检查最近3条记录
                    # 检查RSI指标范围
                    for rsi_field in ['rsi_6', 'rsi_12', 'rsi_24']:
                        rsi_value = record.get(rsi_field)
                        if rsi_value is not None and (rsi_value < 0 or rsi_value > 100):
                            validation_result["warnings"].append(
                                f"RSI指标异常: {record.get('trade_date')} {rsi_field}={rsi_value}"
                            )
            
            # 检查基本面数据
            fundamental_data = stock_data.get("fundamental_data", [])
            if fundamental_data:
                latest_fund = fundamental_data[0]
                pe = latest_fund.get("pe")
                pb = latest_fund.get("pb")
                
                if pe is not None and pe < 0:
                    validation_result["warnings"].append("市盈率为负值，可能为亏损股票")
                
                if pb is not None and pb <= 0:
                    validation_result["warnings"].append("市净率异常")
            
        except Exception as e:
            validation_result["errors"].append(f"数据验证过程出错: {e}")
            validation_result["is_valid"] = False
        
        return validation_result
    
    def remove_outliers(self, values: List[float], method: str = "iqr") -> List[float]:
        """移除异常值"""
        if not values:
            return values
        
        try:
            values_array = np.array([v for v in values if v is not None])
            
            if len(values_array) == 0:
                return values
            
            if method == "iqr":
                # 使用四分位数方法
                q1 = np.percentile(values_array, 25)
                q3 = np.percentile(values_array, 75)
                iqr = q3 - q1
                
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                filtered_values = values_array[
                    (values_array >= lower_bound) & (values_array <= upper_bound)
                ]
                
            elif method == "zscore":
                # 使用Z分数方法
                mean = np.mean(values_array)
                std = np.std(values_array)
                
                if std == 0:
                    return values
                
                z_scores = np.abs((values_array - mean) / std)
                filtered_values = values_array[z_scores < 3]
            
            else:
                return values
            
            logger.info(f"异常值处理: 原始数据{len(values_array)}个，过滤后{len(filtered_values)}个")
            return filtered_values.tolist()
            
        except Exception as e:
            logger.error(f"异常值处理失败: {e}")
            return values
    
    def normalize_data(self, values: List[float], method: str = "minmax") -> List[float]:
        """数据标准化"""
        if not values:
            return values
        
        try:
            values_array = np.array([v for v in values if v is not None])
            
            if len(values_array) == 0:
                return values
            
            if method == "minmax":
                # 最小-最大标准化
                min_val = np.min(values_array)
                max_val = np.max(values_array)
                
                if max_val == min_val:
                    return [0.5] * len(values_array)
                
                normalized = (values_array - min_val) / (max_val - min_val)
                
            elif method == "zscore":
                # Z分数标准化
                mean = np.mean(values_array)
                std = np.std(values_array)
                
                if std == 0:
                    return [0.0] * len(values_array)
                
                normalized = (values_array - mean) / std
            
            else:
                return values
            
            return normalized.tolist()
            
        except Exception as e:
            logger.error(f"数据标准化失败: {e}")
            return values


# 全局数据清洗器实例
data_cleaner = DataCleaner()
