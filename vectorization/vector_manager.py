"""
向量管理模块
"""
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date

from database.vector_client import vector_client
from database.mysql_client import mysql_client
from vectorization.stock_vectorizer import stock_vectorizer
from data_processing.stock_processor import stock_processor
from utils.logger import get_logger

logger = get_logger("vector_manager")


class VectorManager:
    """向量管理器"""
    
    def __init__(self):
        self.batch_size = 100  # 批处理大小
    
    def add_stock_to_vector_db(self, ts_code: str, days: int = 30) -> bool:
        """将股票数据添加到向量数据库"""
        try:
            logger.info(f"开始向量化股票数据: {ts_code}")
            
            # 获取股票综合数据
            stock_data = stock_processor.get_comprehensive_stock_data(ts_code, days)
            if not stock_data:
                logger.warning(f"未获取到股票数据: {ts_code}")
                return False
            
            # 创建文本块
            text_chunks = stock_vectorizer.create_stock_text_chunks(stock_data)
            if not text_chunks:
                logger.warning(f"未创建到文本块: {ts_code}")
                return False
            
            # 向量化文本块
            vectorized_chunks = stock_vectorizer.vectorize_text_chunks(text_chunks)
            if not vectorized_chunks:
                logger.warning(f"向量化失败: {ts_code}")
                return False
            
            # 添加到向量数据库
            documents = []
            metadatas = []
            ids = []
            embeddings = []
            
            for chunk in vectorized_chunks:
                documents.append(chunk["content"])
                metadatas.append(chunk["metadata"])
                ids.append(chunk["id"])
                embeddings.append(chunk["embedding"])
            
            vector_client.add_documents(
                documents=documents,
                metadatas=metadatas,
                ids=ids,
                embeddings=embeddings
            )
            
            # 记录向量索引
            self._record_vector_index(ts_code, vectorized_chunks)
            
            logger.info(f"成功向量化股票数据: {ts_code}, 创建了 {len(vectorized_chunks)} 个向量")
            return True
            
        except Exception as e:
            logger.error(f"向量化股票数据失败 {ts_code}: {e}")
            return False
    
    def update_stock_vectors(self, ts_code: str, days: int = 30) -> bool:
        """更新股票向量数据"""
        try:
            logger.info(f"开始更新股票向量: {ts_code}")
            
            # 删除现有向量
            self.delete_stock_vectors(ts_code)
            
            # 重新添加向量
            return self.add_stock_to_vector_db(ts_code, days)
            
        except Exception as e:
            logger.error(f"更新股票向量失败 {ts_code}: {e}")
            return False
    
    def delete_stock_vectors(self, ts_code: str) -> bool:
        """删除股票向量数据"""
        try:
            # 获取该股票的所有向量ID
            vector_ids = self._get_stock_vector_ids(ts_code)
            
            if vector_ids:
                vector_client.delete_documents(vector_ids)
                logger.info(f"删除股票向量: {ts_code}, 删除了 {len(vector_ids)} 个向量")
            
            # 删除向量索引记录
            self._delete_vector_index_records(ts_code)
            
            return True
            
        except Exception as e:
            logger.error(f"删除股票向量失败 {ts_code}: {e}")
            return False
    
    def search_similar_stocks(
        self, 
        query: str, 
        n_results: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似股票"""
        try:
            # 构建查询条件
            where_condition = {}
            if filters:
                if filters.get("industry"):
                    where_condition["industry"] = filters["industry"]
                if filters.get("data_type"):
                    where_condition["data_type"] = filters["data_type"]
                if filters.get("ts_code"):
                    where_condition["ts_code"] = filters["ts_code"]
            
            # 执行向量搜索
            results = vector_client.query_documents(
                query_texts=[query],
                n_results=n_results,
                where=where_condition if where_condition else None
            )
            
            # 格式化结果
            formatted_results = []
            if results and results.get("ids") and results["ids"][0]:
                for i, doc_id in enumerate(results["ids"][0]):
                    result = {
                        "id": doc_id,
                        "content": results["documents"][0][i] if results.get("documents") else "",
                        "metadata": results["metadatas"][0][i] if results.get("metadatas") else {},
                        "distance": results["distances"][0][i] if results.get("distances") else 0
                    }
                    formatted_results.append(result)
            
            logger.info(f"向量搜索完成，查询: '{query}', 返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []
    
    def get_stock_context(self, ts_code: str, context_types: Optional[List[str]] = None) -> str:
        """获取股票上下文信息"""
        try:
            # 构建查询条件
            where_condition = {"ts_code": ts_code}
            if context_types:
                # 如果指定了上下文类型，只获取特定类型的数据
                pass  # ChromaDB的where条件比较有限，这里先获取所有数据再过滤
            
            # 搜索该股票的所有向量
            results = vector_client.query_documents(
                query_texts=[f"股票代码 {ts_code}"],
                n_results=50,  # 获取更多结果
                where=where_condition
            )
            
            # 组装上下文
            context_parts = []
            if results and results.get("documents") and results["documents"][0]:
                for i, content in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i] if results.get("metadatas") else {}
                    
                    # 如果指定了上下文类型，进行过滤
                    if context_types and metadata.get("data_type") not in context_types:
                        continue
                    
                    context_parts.append(content)
            
            context = "\n\n".join(context_parts)
            logger.info(f"获取股票上下文: {ts_code}, 长度: {len(context)}")
            return context
            
        except Exception as e:
            logger.error(f"获取股票上下文失败 {ts_code}: {e}")
            return ""
    
    def batch_vectorize_stocks(self, ts_codes: List[str], days: int = 30) -> Dict[str, bool]:
        """批量向量化股票"""
        results = {}
        
        try:
            logger.info(f"开始批量向量化 {len(ts_codes)} 只股票")
            
            for i, ts_code in enumerate(ts_codes):
                try:
                    success = self.add_stock_to_vector_db(ts_code, days)
                    results[ts_code] = success
                    
                    if (i + 1) % 10 == 0:
                        logger.info(f"批量向量化进度: {i + 1}/{len(ts_codes)}")
                        
                except Exception as e:
                    logger.error(f"向量化股票失败 {ts_code}: {e}")
                    results[ts_code] = False
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"批量向量化完成: 成功 {success_count}/{len(ts_codes)}")
            
            return results
            
        except Exception as e:
            logger.error(f"批量向量化失败: {e}")
            return results
    
    def get_vector_statistics(self) -> Dict[str, Any]:
        """获取向量数据库统计信息"""
        try:
            # 获取集合信息
            collection_info = vector_client.get_collection_info()
            
            # 查询不同数据类型的统计
            data_types = ["basic_info", "price_data", "technical_macd", "technical_kdj", 
                         "technical_rsi", "technical_boll", "fundamental", "money_flow", 
                         "trend_analysis"]
            
            type_stats = {}
            for data_type in data_types:
                try:
                    results = vector_client.query_documents(
                        query_texts=["统计"],
                        n_results=1000,
                        where={"data_type": data_type}
                    )
                    count = len(results["ids"][0]) if results.get("ids") and results["ids"][0] else 0
                    type_stats[data_type] = count
                except:
                    type_stats[data_type] = 0
            
            # 获取股票数量（通过ts_code去重）
            try:
                all_results = vector_client.query_documents(
                    query_texts=["股票"],
                    n_results=10000
                )
                
                unique_stocks = set()
                if all_results.get("metadatas") and all_results["metadatas"][0]:
                    for metadata in all_results["metadatas"][0]:
                        ts_code = metadata.get("ts_code")
                        if ts_code:
                            unique_stocks.add(ts_code)
                
                stock_count = len(unique_stocks)
            except:
                stock_count = 0
            
            statistics = {
                "collection_info": collection_info,
                "total_vectors": collection_info.get("count", 0),
                "unique_stocks": stock_count,
                "data_type_distribution": type_stats,
                "generated_at": datetime.now().isoformat()
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"获取向量统计信息失败: {e}")
            return {}
    
    def _record_vector_index(self, ts_code: str, vectorized_chunks: List[Dict[str, Any]]) -> None:
        """记录向量索引到MySQL"""
        try:
            # 这里可以创建一个向量索引表来记录向量ID和股票代码的对应关系
            # 暂时跳过，因为我们还没有创建这个表
            pass
            
        except Exception as e:
            logger.error(f"记录向量索引失败 {ts_code}: {e}")
    
    def _delete_vector_index_records(self, ts_code: str) -> None:
        """删除向量索引记录"""
        try:
            # 这里删除MySQL中的向量索引记录
            # 暂时跳过
            pass
            
        except Exception as e:
            logger.error(f"删除向量索引记录失败 {ts_code}: {e}")
    
    def _get_stock_vector_ids(self, ts_code: str) -> List[str]:
        """获取股票的所有向量ID"""
        try:
            # 通过搜索获取该股票的所有向量ID
            results = vector_client.query_documents(
                query_texts=[f"股票代码 {ts_code}"],
                n_results=1000,  # 获取足够多的结果
                where={"ts_code": ts_code}
            )
            
            if results and results.get("ids") and results["ids"][0]:
                return results["ids"][0]
            
            return []
            
        except Exception as e:
            logger.error(f"获取股票向量ID失败 {ts_code}: {e}")
            return []


# 全局向量管理器实例
vector_manager = VectorManager()
