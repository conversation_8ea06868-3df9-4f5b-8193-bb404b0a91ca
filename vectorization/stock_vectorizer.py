"""
股票数据向量化模块
"""
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date

from llm.model_manager import model_manager, ModelType
from data_processing.stock_processor import stock_processor
from data_processing.data_cleaner import data_cleaner
from utils.logger import get_logger

logger = get_logger("stock_vectorizer")


class StockVectorizer:
    """股票数据向量化器"""

    def __init__(self):
        self.chunk_size = 500  # 文本块大小
        self.chunk_overlap = 50  # 文本块重叠

    def _clean_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """清理元数据，确保类型兼容ChromaDB"""
        cleaned = {}
        for key, value in metadata.items():
            if value is None:
                cleaned[key] = ""
            elif isinstance(value, (date, datetime)):
                cleaned[key] = str(value)
            elif isinstance(value, (int, float)):
                # 确保数值类型正确
                try:
                    if isinstance(value, float) and value.is_integer():
                        cleaned[key] = int(value)
                    else:
                        cleaned[key] = float(value)
                except (ValueError, AttributeError):
                    cleaned[key] = 0
            else:
                cleaned[key] = str(value)
        return cleaned
    
    def create_stock_text_chunks(self, stock_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """将股票数据创建为文本块"""
        chunks = []
        
        try:
            basic_info = stock_data.get("basic_info", {})
            ts_code = basic_info.get("ts_code", "")
            stock_name = basic_info.get("name", "")
            
            # 1. 基础信息块
            basic_chunk = self._create_basic_info_chunk(stock_data)
            if basic_chunk:
                chunks.append(basic_chunk)
            
            # 2. 最新价格数据块
            price_chunk = self._create_price_data_chunk(stock_data)
            if price_chunk:
                chunks.append(price_chunk)
            
            # 3. 技术指标数据块
            technical_chunks = self._create_technical_data_chunks(stock_data)
            chunks.extend(technical_chunks)
            
            # 4. 基本面数据块
            fundamental_chunk = self._create_fundamental_data_chunk(stock_data)
            if fundamental_chunk:
                chunks.append(fundamental_chunk)
            
            # 5. 资金流向数据块
            money_flow_chunk = self._create_money_flow_data_chunk(stock_data)
            if money_flow_chunk:
                chunks.append(money_flow_chunk)
            
            # 6. 历史趋势分析块
            trend_chunks = self._create_trend_analysis_chunks(stock_data)
            chunks.extend(trend_chunks)
            
            logger.info(f"为股票 {ts_code}({stock_name}) 创建了 {len(chunks)} 个文本块")
            return chunks
            
        except Exception as e:
            logger.error(f"创建股票文本块失败: {e}")
            return []
    
    def _create_basic_info_chunk(self, stock_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建基础信息文本块"""
        try:
            basic_info = stock_data.get("basic_info", {})
            if not basic_info:
                return None
            
            text_parts = [
                f"股票基础信息：",
                f"股票代码：{basic_info.get('ts_code', 'N/A')}",
                f"股票名称：{basic_info.get('name', 'N/A')}",
                f"所属行业：{basic_info.get('industry', 'N/A')}",
                f"所属地区：{basic_info.get('area', 'N/A')}",
                f"上市日期：{basic_info.get('list_date', 'N/A')}"
            ]
            
            metadata = {
                "ts_code": basic_info.get("ts_code", ""),
                "stock_name": basic_info.get("name", ""),
                "data_type": "basic_info",
                "industry": basic_info.get("industry", ""),
                "area": basic_info.get("area", ""),
                "list_date": basic_info.get("list_date", ""),
                "created_at": datetime.now().isoformat()
            }

            return {
                "id": f"{basic_info.get('ts_code', '')}_basic_info",
                "content": "\n".join(text_parts),
                "metadata": self._clean_metadata(metadata)
            }
            
        except Exception as e:
            logger.error(f"创建基础信息块失败: {e}")
            return None
    
    def _create_price_data_chunk(self, stock_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建价格数据文本块"""
        try:
            daily_data = stock_data.get("daily_data", [])
            if not daily_data:
                return None
            
            basic_info = stock_data.get("basic_info", {})
            ts_code = basic_info.get("ts_code", "")
            stock_name = basic_info.get("name", "")
            
            # 取最近5天的数据
            recent_data = daily_data[:5]
            
            text_parts = [f"{stock_name}({ts_code}) 最近价格数据："]
            
            for record in recent_data:
                trade_date = record.get("trade_date", "")
                close = record.get("close", 0)
                pct_chg = record.get("pct_chg", 0)
                vol = record.get("vol", 0)
                amount = record.get("amount", 0)
                
                text_parts.append(
                    f"{trade_date}: 收盘价{close}元, 涨跌幅{pct_chg}%, "
                    f"成交量{vol}手, 成交额{amount}千元"
                )
            
            metadata = {
                "ts_code": ts_code,
                "stock_name": stock_name,
                "data_type": "price_data",
                "latest_date": recent_data[0].get("trade_date", "") if recent_data else "",
                "latest_price": recent_data[0].get("close", 0) if recent_data else 0,
                "created_at": datetime.now().isoformat()
            }

            return {
                "id": f"{ts_code}_price_data",
                "content": "\n".join(text_parts),
                "metadata": self._clean_metadata(metadata)
            }
            
        except Exception as e:
            logger.error(f"创建价格数据块失败: {e}")
            return None
    
    def _create_technical_data_chunks(self, stock_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建技术指标数据块"""
        chunks = []
        
        try:
            technical_data = stock_data.get("technical_data", [])
            if not technical_data:
                return chunks
            
            basic_info = stock_data.get("basic_info", {})
            ts_code = basic_info.get("ts_code", "")
            stock_name = basic_info.get("name", "")
            
            # 最新技术指标
            latest_tech = technical_data[0]
            
            # MACD指标块
            macd_text = [
                f"{stock_name}({ts_code}) MACD技术指标：",
                f"日期：{latest_tech.get('trade_date', '')}",
                f"DIF：{latest_tech.get('macd_dif', 'N/A')}",
                f"DEA：{latest_tech.get('macd_dea', 'N/A')}",
                f"MACD：{latest_tech.get('macd', 'N/A')}"
            ]
            
            metadata = {
                "ts_code": ts_code,
                "stock_name": stock_name,
                "data_type": "technical_macd",
                "date": latest_tech.get("trade_date", ""),
                "created_at": datetime.now().isoformat()
            }

            chunks.append({
                "id": f"{ts_code}_macd",
                "content": "\n".join(macd_text),
                "metadata": self._clean_metadata(metadata)
            })
            
            # KDJ指标块
            kdj_text = [
                f"{stock_name}({ts_code}) KDJ技术指标：",
                f"日期：{latest_tech.get('trade_date', '')}",
                f"K值：{latest_tech.get('kdj_k', 'N/A')}",
                f"D值：{latest_tech.get('kdj_d', 'N/A')}",
                f"J值：{latest_tech.get('kdj_j', 'N/A')}"
            ]
            
            chunks.append({
                "id": f"{ts_code}_kdj",
                "content": "\n".join(kdj_text),
                "metadata": {
                    "ts_code": ts_code,
                    "stock_name": stock_name,
                    "data_type": "technical_kdj",
                    "date": latest_tech.get("trade_date", ""),
                    "created_at": datetime.now().isoformat()
                }
            })
            
            # RSI指标块
            rsi_text = [
                f"{stock_name}({ts_code}) RSI技术指标：",
                f"日期：{latest_tech.get('trade_date', '')}",
                f"RSI6：{latest_tech.get('rsi_6', 'N/A')}",
                f"RSI12：{latest_tech.get('rsi_12', 'N/A')}",
                f"RSI24：{latest_tech.get('rsi_24', 'N/A')}"
            ]
            
            chunks.append({
                "id": f"{ts_code}_rsi",
                "content": "\n".join(rsi_text),
                "metadata": {
                    "ts_code": ts_code,
                    "stock_name": stock_name,
                    "data_type": "technical_rsi",
                    "date": latest_tech.get("trade_date", ""),
                    "created_at": datetime.now().isoformat()
                }
            })
            
            # 布林带指标块
            boll_text = [
                f"{stock_name}({ts_code}) 布林带技术指标：",
                f"日期：{latest_tech.get('trade_date', '')}",
                f"上轨：{latest_tech.get('boll_upper', 'N/A')}",
                f"中轨：{latest_tech.get('boll_mid', 'N/A')}",
                f"下轨：{latest_tech.get('boll_lower', 'N/A')}"
            ]
            
            chunks.append({
                "id": f"{ts_code}_boll",
                "content": "\n".join(boll_text),
                "metadata": {
                    "ts_code": ts_code,
                    "stock_name": stock_name,
                    "data_type": "technical_boll",
                    "date": latest_tech.get("trade_date", ""),
                    "created_at": datetime.now().isoformat()
                }
            })
            
        except Exception as e:
            logger.error(f"创建技术指标块失败: {e}")
        
        return chunks
    
    def _create_fundamental_data_chunk(self, stock_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建基本面数据块"""
        try:
            fundamental_data = stock_data.get("fundamental_data", [])
            if not fundamental_data:
                return None
            
            basic_info = stock_data.get("basic_info", {})
            ts_code = basic_info.get("ts_code", "")
            stock_name = basic_info.get("name", "")
            
            latest_fund = fundamental_data[0]
            
            text_parts = [
                f"{stock_name}({ts_code}) 基本面数据：",
                f"日期：{latest_fund.get('trade_date', '')}",
                f"市盈率：PE={latest_fund.get('pe', 'N/A')}, PE_TTM={latest_fund.get('pe_ttm', 'N/A')}",
                f"市净率：{latest_fund.get('pb', 'N/A')}",
                f"市销率：PS={latest_fund.get('ps', 'N/A')}, PS_TTM={latest_fund.get('ps_ttm', 'N/A')}",
                f"换手率：{latest_fund.get('turnover_rate', 'N/A')}%",
                f"总市值：{latest_fund.get('total_mv', 'N/A')}万元",
                f"流通市值：{latest_fund.get('circ_mv', 'N/A')}万元"
            ]
            
            return {
                "id": f"{ts_code}_fundamental",
                "content": "\n".join(text_parts),
                "metadata": {
                    "ts_code": ts_code,
                    "stock_name": stock_name,
                    "data_type": "fundamental",
                    "date": latest_fund.get("trade_date", ""),
                    "pe": latest_fund.get("pe", 0),
                    "pb": latest_fund.get("pb", 0),
                    "total_mv": latest_fund.get("total_mv", 0),
                    "created_at": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"创建基本面数据块失败: {e}")
            return None
    
    def _create_money_flow_data_chunk(self, stock_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建资金流向数据块"""
        try:
            money_flow_data = stock_data.get("money_flow_data", [])
            if not money_flow_data:
                return None
            
            basic_info = stock_data.get("basic_info", {})
            ts_code = basic_info.get("ts_code", "")
            stock_name = basic_info.get("name", "")
            
            latest_money = money_flow_data[0]
            
            # 计算各类资金净流入
            lg_net = (latest_money.get("buy_lg_amount", 0) or 0) - (latest_money.get("sell_lg_amount", 0) or 0)
            md_net = (latest_money.get("buy_md_amount", 0) or 0) - (latest_money.get("sell_md_amount", 0) or 0)
            sm_net = (latest_money.get("buy_sm_amount", 0) or 0) - (latest_money.get("sell_sm_amount", 0) or 0)
            
            text_parts = [
                f"{stock_name}({ts_code}) 资金流向数据：",
                f"日期：{latest_money.get('trade_date', '')}",
                f"总净流入：{latest_money.get('net_mf_amount', 'N/A')}万元",
                f"大单净流入：{lg_net}万元",
                f"中单净流入：{md_net}万元",
                f"小单净流入：{sm_net}万元"
            ]
            
            return {
                "id": f"{ts_code}_money_flow",
                "content": "\n".join(text_parts),
                "metadata": {
                    "ts_code": ts_code,
                    "stock_name": stock_name,
                    "data_type": "money_flow",
                    "date": latest_money.get("trade_date", ""),
                    "net_mf_amount": latest_money.get("net_mf_amount", 0),
                    "lg_net_flow": lg_net,
                    "created_at": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"创建资金流向数据块失败: {e}")
            return None
    
    def _create_trend_analysis_chunks(self, stock_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建趋势分析块"""
        chunks = []
        
        try:
            daily_data = stock_data.get("daily_data", [])
            if len(daily_data) < 5:
                return chunks
            
            basic_info = stock_data.get("basic_info", {})
            ts_code = basic_info.get("ts_code", "")
            stock_name = basic_info.get("name", "")
            
            # 短期趋势分析（5天）
            short_term_data = daily_data[:5]
            short_term_analysis = self._analyze_price_trend(short_term_data, "短期")
            
            if short_term_analysis:
                chunks.append({
                    "id": f"{ts_code}_short_trend",
                    "content": f"{stock_name}({ts_code}) {short_term_analysis}",
                    "metadata": {
                        "ts_code": ts_code,
                        "stock_name": stock_name,
                        "data_type": "trend_analysis",
                        "trend_type": "short_term",
                        "created_at": datetime.now().isoformat()
                    }
                })
            
            # 中期趋势分析（20天）
            if len(daily_data) >= 20:
                medium_term_data = daily_data[:20]
                medium_term_analysis = self._analyze_price_trend(medium_term_data, "中期")
                
                if medium_term_analysis:
                    chunks.append({
                        "id": f"{ts_code}_medium_trend",
                        "content": f"{stock_name}({ts_code}) {medium_term_analysis}",
                        "metadata": {
                            "ts_code": ts_code,
                            "stock_name": stock_name,
                            "data_type": "trend_analysis",
                            "trend_type": "medium_term",
                            "created_at": datetime.now().isoformat()
                        }
                    })
            
        except Exception as e:
            logger.error(f"创建趋势分析块失败: {e}")
        
        return chunks
    
    def _analyze_price_trend(self, price_data: List[Dict[str, Any]], period: str) -> str:
        """分析价格趋势"""
        try:
            if len(price_data) < 2:
                return ""
            
            # 计算价格变化
            prices = [record.get("close", 0) for record in price_data if record.get("close")]
            if len(prices) < 2:
                return ""
            
            # 计算趋势
            start_price = prices[-1]  # 最早价格
            end_price = prices[0]     # 最新价格
            price_change = end_price - start_price
            price_change_pct = (price_change / start_price) * 100 if start_price > 0 else 0
            
            # 计算波动率
            price_changes = []
            for i in range(len(prices) - 1):
                change = (prices[i] - prices[i + 1]) / prices[i + 1] * 100 if prices[i + 1] > 0 else 0
                price_changes.append(abs(change))
            
            avg_volatility = sum(price_changes) / len(price_changes) if price_changes else 0
            
            # 趋势判断
            if price_change_pct > 5:
                trend = "强势上涨"
            elif price_change_pct > 0:
                trend = "温和上涨"
            elif price_change_pct > -5:
                trend = "温和下跌"
            else:
                trend = "强势下跌"
            
            # 波动性判断
            if avg_volatility > 3:
                volatility = "高波动"
            elif avg_volatility > 1:
                volatility = "中等波动"
            else:
                volatility = "低波动"
            
            analysis_text = (
                f"{period}趋势分析：{trend}，价格变化{price_change_pct:.2f}%，"
                f"平均波动率{avg_volatility:.2f}%，属于{volatility}。"
            )
            
            return analysis_text
            
        except Exception as e:
            logger.error(f"分析价格趋势失败: {e}")
            return ""
    
    def vectorize_text_chunks(self, text_chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对文本块进行向量化"""
        vectorized_chunks = []
        
        try:
            for chunk in text_chunks:
                content = chunk.get("content", "")
                if not content:
                    continue
                
                try:
                    # 生成向量嵌入
                    embedding = model_manager.generate_embeddings(content)
                    
                    vectorized_chunk = {
                        "id": chunk["id"],
                        "content": content,
                        "metadata": self._clean_metadata(chunk["metadata"]),
                        "embedding": embedding
                    }
                    
                    vectorized_chunks.append(vectorized_chunk)
                    
                except Exception as e:
                    logger.error(f"向量化文本块失败 {chunk['id']}: {e}")
                    continue
            
            logger.info(f"成功向量化 {len(vectorized_chunks)} 个文本块")
            return vectorized_chunks
            
        except Exception as e:
            logger.error(f"批量向量化失败: {e}")
            return []


# 全局股票向量化器实例
stock_vectorizer = StockVectorizer()
