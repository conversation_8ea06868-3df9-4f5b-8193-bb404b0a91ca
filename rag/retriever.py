"""
RAG检索引擎模块
"""
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date

from database.vector_client import vector_client
from llm.model_manager import model_manager, ModelType
from vectorization.vector_manager import vector_manager
from utils.logger import get_logger

logger = get_logger("retriever")


class RAGRetriever:
    """RAG检索引擎"""
    
    def __init__(self):
        self.default_top_k = 10
        self.similarity_threshold = 0.3  # 降低相似度阈值
        self.max_context_length = 4000
    
    def search_relevant_context(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        top_k: Optional[int] = None,
        include_similarity_scores: bool = True
    ) -> List[Dict[str, Any]]:
        """搜索相关上下文"""
        try:
            top_k = top_k or self.default_top_k
            
            logger.info(f"开始RAG检索，查询: '{query}', top_k: {top_k}")
            
            # 生成查询向量
            query_embedding = model_manager.generate_embeddings(query)
            
            # 构建过滤条件
            where_condition = None
            if filters:
                # 如果filters已经是正确的ChromaDB格式，直接使用
                if "$and" in filters or "$or" in filters:
                    where_condition = filters
                else:
                    # 转换为ChromaDB格式
                    conditions = []
                    for key, value in filters.items():
                        if value is not None:
                            conditions.append({key: {"$eq": value}})

                    if len(conditions) == 1:
                        where_condition = conditions[0]
                    elif len(conditions) > 1:
                        where_condition = {"$and": conditions}
            
            # 执行向量搜索
            results = vector_client.query_documents(
                query_embeddings=[query_embedding],  # 使用向量而不是文本
                n_results=top_k,
                where=where_condition
            )
            
            # 格式化结果
            formatted_results = []
            if results and results.get("ids") and results["ids"][0]:
                for i, doc_id in enumerate(results["ids"][0]):
                    distance = results["distances"][0][i] if results.get("distances") else float('inf')

                    # 对于欧几里得距离，使用倒数作为相似度分数
                    # 距离越小，相似度越高
                    if distance == 0:
                        similarity_score = 1.0
                    else:
                        # 使用归一化的相似度分数
                        similarity_score = 1.0 / (1.0 + distance / 100.0)

                    # 过滤低相似度结果 - 降低阈值
                    if similarity_score < 0.1:  # 更低的阈值
                        continue
                    
                    result = {
                        "id": doc_id,
                        "content": results["documents"][0][i] if results.get("documents") else "",
                        "metadata": results["metadatas"][0][i] if results.get("metadatas") else {},
                        "similarity_score": similarity_score
                    }
                    
                    if not include_similarity_scores:
                        result.pop("similarity_score", None)
                    
                    formatted_results.append(result)
            
            logger.info(f"RAG检索完成，返回 {len(formatted_results)} 个相关结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"RAG检索失败: {e}")
            return []
    
    def search_stock_context(
        self,
        stock_code: str,
        context_types: Optional[List[str]] = None,
        top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """搜索特定股票的上下文"""
        try:
            # 构建过滤条件 - ChromaDB只支持单个条件或$and操作
            if context_types and len(context_types) == 1:
                filters = {
                    "$and": [
                        {"ts_code": {"$eq": stock_code}},
                        {"data_type": {"$eq": context_types[0]}}
                    ]
                }
            else:
                filters = {"ts_code": {"$eq": stock_code}}

            query = f"股票代码 {stock_code}"
            results = self.search_relevant_context(
                query=query,
                filters=filters,
                top_k=top_k or 20  # 股票特定搜索返回更多结果
            )
            
            # 如果指定了多个上下文类型，在结果中过滤
            if context_types and len(context_types) > 1:
                filtered_results = []
                for result in results:
                    metadata = result.get("metadata", {})
                    if metadata.get("data_type") in context_types:
                        filtered_results.append(result)
                results = filtered_results
            
            logger.info(f"股票 {stock_code} 上下文检索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"股票上下文检索失败 {stock_code}: {e}")
            return []
    
    def search_by_industry(
        self,
        industry: str,
        query: Optional[str] = None,
        top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """按行业搜索相关股票"""
        try:
            filters = {"industry": {"$eq": industry}}
            search_query = query or f"{industry}行业股票"
            
            results = self.search_relevant_context(
                query=search_query,
                filters=filters,
                top_k=top_k
            )
            
            logger.info(f"行业 {industry} 检索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"行业检索失败 {industry}: {e}")
            return []
    
    def search_by_data_type(
        self,
        data_type: str,
        query: Optional[str] = None,
        top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """按数据类型搜索"""
        try:
            filters = {"data_type": {"$eq": data_type}}
            search_query = query or f"{data_type}数据"
            
            results = self.search_relevant_context(
                query=search_query,
                filters=filters,
                top_k=top_k
            )
            
            logger.info(f"数据类型 {data_type} 检索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"数据类型检索失败 {data_type}: {e}")
            return []
    
    def hybrid_search(
        self,
        query: str,
        stock_codes: Optional[List[str]] = None,
        industries: Optional[List[str]] = None,
        data_types: Optional[List[str]] = None,
        top_k: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """混合搜索：结合多种过滤条件"""
        try:
            all_results = []
            
            # 基础语义搜索
            base_results = self.search_relevant_context(query=query, top_k=top_k)
            all_results.extend(base_results)
            
            # 股票代码过滤搜索
            if stock_codes:
                for stock_code in stock_codes:
                    stock_results = self.search_stock_context(
                        stock_code=stock_code,
                        top_k=5  # 每只股票返回较少结果
                    )
                    all_results.extend(stock_results)
            
            # 行业过滤搜索
            if industries:
                for industry in industries:
                    industry_results = self.search_by_industry(
                        industry=industry,
                        query=query,
                        top_k=5
                    )
                    all_results.extend(industry_results)
            
            # 数据类型过滤搜索
            if data_types:
                for data_type in data_types:
                    type_results = self.search_by_data_type(
                        data_type=data_type,
                        query=query,
                        top_k=5
                    )
                    all_results.extend(type_results)
            
            # 去重和排序
            unique_results = self._deduplicate_results(all_results)
            sorted_results = sorted(
                unique_results,
                key=lambda x: x.get("similarity_score", 0),
                reverse=True
            )
            
            # 限制结果数量
            final_results = sorted_results[:top_k or self.default_top_k]
            
            logger.info(f"混合搜索完成，返回 {len(final_results)} 个结果")
            return final_results
            
        except Exception as e:
            logger.error(f"混合搜索失败: {e}")
            return []
    
    def get_context_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取检索结果的摘要信息"""
        try:
            if not results:
                return {"total": 0, "data_types": {}, "stocks": set(), "industries": set()}
            
            summary = {
                "total": len(results),
                "data_types": {},
                "stocks": set(),
                "industries": set(),
                "avg_similarity": 0.0
            }
            
            similarity_scores = []
            
            for result in results:
                metadata = result.get("metadata", {})
                
                # 统计数据类型
                data_type = metadata.get("data_type", "unknown")
                summary["data_types"][data_type] = summary["data_types"].get(data_type, 0) + 1
                
                # 收集股票代码
                ts_code = metadata.get("ts_code")
                if ts_code:
                    summary["stocks"].add(ts_code)
                
                # 收集行业信息
                industry = metadata.get("industry")
                if industry:
                    summary["industries"].add(industry)
                
                # 收集相似度分数
                similarity = result.get("similarity_score")
                if similarity is not None:
                    similarity_scores.append(similarity)
            
            # 计算平均相似度
            if similarity_scores:
                summary["avg_similarity"] = sum(similarity_scores) / len(similarity_scores)
            
            # 转换集合为列表
            summary["stocks"] = list(summary["stocks"])
            summary["industries"] = list(summary["industries"])
            
            return summary
            
        except Exception as e:
            logger.error(f"生成上下文摘要失败: {e}")
            return {"total": 0, "data_types": {}, "stocks": [], "industries": []}
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重检索结果"""
        seen_ids = set()
        unique_results = []
        
        for result in results:
            result_id = result.get("id")
            if result_id and result_id not in seen_ids:
                seen_ids.add(result_id)
                unique_results.append(result)
        
        return unique_results
    
    def build_context_text(
        self,
        results: List[Dict[str, Any]],
        max_length: Optional[int] = None
    ) -> str:
        """构建上下文文本"""
        try:
            max_length = max_length or self.max_context_length
            
            context_parts = []
            current_length = 0
            
            for result in results:
                content = result.get("content", "")
                if not content:
                    continue
                
                # 检查长度限制
                if current_length + len(content) > max_length:
                    # 如果添加这个内容会超出限制，截断内容
                    remaining_length = max_length - current_length
                    if remaining_length > 100:  # 至少保留100字符
                        content = content[:remaining_length] + "..."
                        context_parts.append(content)
                    break
                
                context_parts.append(content)
                current_length += len(content)
            
            context_text = "\n\n".join(context_parts)
            
            logger.info(f"构建上下文文本完成，长度: {len(context_text)}")
            return context_text
            
        except Exception as e:
            logger.error(f"构建上下文文本失败: {e}")
            return ""


# 全局RAG检索器实例
rag_retriever = RAGRetriever()
