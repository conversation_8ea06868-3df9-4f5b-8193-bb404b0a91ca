"""
上下文构建模块
"""
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
import json

from rag.retriever import rag_retriever
from data_processing.stock_processor import stock_processor
from utils.logger import get_logger

logger = get_logger("context_builder")


class ContextBuilder:
    """上下文构建器"""
    
    def __init__(self):
        self.max_context_length = 4000
        self.context_templates = {
            "stock_analysis": self._build_stock_analysis_context,
            "industry_comparison": self._build_industry_comparison_context,
            "technical_analysis": self._build_technical_analysis_context,
            "fundamental_analysis": self._build_fundamental_analysis_context,
            "general_query": self._build_general_query_context
        }
    
    def build_context(
        self,
        query: str,
        context_type: str = "general_query",
        stock_codes: Optional[List[str]] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        max_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """构建查询上下文"""
        try:
            max_length = max_length or self.max_context_length
            
            logger.info(f"开始构建上下文，类型: {context_type}, 查询: '{query}'")
            
            # 选择合适的上下文构建方法
            builder_func = self.context_templates.get(
                context_type, 
                self._build_general_query_context
            )
            
            # 构建上下文
            context_data = builder_func(
                query=query,
                stock_codes=stock_codes,
                additional_filters=additional_filters,
                max_length=max_length
            )
            
            # 添加元信息
            context_data.update({
                "query": query,
                "context_type": context_type,
                "generated_at": datetime.now().isoformat(),
                "max_length": max_length
            })
            
            logger.info(f"上下文构建完成，类型: {context_type}")
            return context_data
            
        except Exception as e:
            logger.error(f"构建上下文失败: {e}")
            return {
                "context_text": "",
                "sources": [],
                "summary": {},
                "error": str(e)
            }
    
    def _build_stock_analysis_context(
        self,
        query: str,
        stock_codes: Optional[List[str]] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        max_length: int = 4000
    ) -> Dict[str, Any]:
        """构建股票分析上下文"""
        try:
            context_parts = []
            all_sources = []
            
            if stock_codes:
                # 为每只股票构建详细上下文
                for stock_code in stock_codes:
                    stock_context = self._build_single_stock_context(
                        stock_code, 
                        query,
                        max_length // len(stock_codes)
                    )
                    
                    if stock_context["context_text"]:
                        context_parts.append(f"=== {stock_code} 股票信息 ===")
                        context_parts.append(stock_context["context_text"])
                        all_sources.extend(stock_context["sources"])
            else:
                # 通用股票搜索
                results = rag_retriever.search_relevant_context(
                    query=query,
                    filters=additional_filters,
                    top_k=15
                )
                
                context_text = rag_retriever.build_context_text(results, max_length)
                context_parts.append(context_text)
                all_sources = results
            
            final_context = "\n\n".join(context_parts)
            summary = rag_retriever.get_context_summary(all_sources)
            
            return {
                "context_text": final_context,
                "sources": all_sources,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"构建股票分析上下文失败: {e}")
            return {"context_text": "", "sources": [], "summary": {}}
    
    def _build_single_stock_context(
        self,
        stock_code: str,
        query: str,
        max_length: int = 1000
    ) -> Dict[str, Any]:
        """构建单只股票的上下文"""
        try:
            # 获取股票的多维度数据
            context_types = [
                "basic_info", "price_data", "technical_macd", "technical_kdj",
                "technical_rsi", "fundamental", "money_flow"
            ]
            
            all_results = []
            for context_type in context_types:
                results = rag_retriever.search_stock_context(
                    stock_code=stock_code,
                    context_types=[context_type],
                    top_k=2
                )
                all_results.extend(results)
            
            # 构建上下文文本
            context_text = rag_retriever.build_context_text(all_results, max_length)
            
            return {
                "context_text": context_text,
                "sources": all_results
            }
            
        except Exception as e:
            logger.error(f"构建单只股票上下文失败 {stock_code}: {e}")
            return {"context_text": "", "sources": []}
    
    def _build_industry_comparison_context(
        self,
        query: str,
        stock_codes: Optional[List[str]] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        max_length: int = 4000
    ) -> Dict[str, Any]:
        """构建行业比较上下文"""
        try:
            context_parts = []
            all_sources = []
            
            # 如果指定了股票代码，获取它们的行业信息
            industries = set()
            if stock_codes:
                for stock_code in stock_codes:
                    basic_info = stock_processor.get_stock_basic_info(stock_code)
                    if basic_info and basic_info.get("industry"):
                        industries.add(basic_info["industry"])
            
            # 如果没有指定股票或从过滤条件中获取行业
            if additional_filters and additional_filters.get("industries"):
                industries.update(additional_filters["industries"])
            
            # 为每个行业构建上下文
            if industries:
                for industry in industries:
                    industry_results = rag_retriever.search_by_industry(
                        industry=industry,
                        query=query,
                        top_k=8
                    )
                    
                    if industry_results:
                        context_parts.append(f"=== {industry}行业 ===")
                        industry_context = rag_retriever.build_context_text(
                            industry_results, 
                            max_length // len(industries)
                        )
                        context_parts.append(industry_context)
                        all_sources.extend(industry_results)
            else:
                # 通用行业搜索
                results = rag_retriever.search_relevant_context(
                    query=f"行业 {query}",
                    top_k=15
                )
                context_text = rag_retriever.build_context_text(results, max_length)
                context_parts.append(context_text)
                all_sources = results
            
            final_context = "\n\n".join(context_parts)
            summary = rag_retriever.get_context_summary(all_sources)
            
            return {
                "context_text": final_context,
                "sources": all_sources,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"构建行业比较上下文失败: {e}")
            return {"context_text": "", "sources": [], "summary": {}}
    
    def _build_technical_analysis_context(
        self,
        query: str,
        stock_codes: Optional[List[str]] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        max_length: int = 4000
    ) -> Dict[str, Any]:
        """构建技术分析上下文"""
        try:
            # 技术分析相关的数据类型
            technical_data_types = [
                "technical_macd", "technical_kdj", "technical_rsi", 
                "technical_boll", "price_data", "trend_analysis"
            ]
            
            all_results = []
            
            if stock_codes:
                # 为指定股票获取技术分析数据
                for stock_code in stock_codes:
                    for data_type in technical_data_types:
                        results = rag_retriever.search_stock_context(
                            stock_code=stock_code,
                            context_types=[data_type],
                            top_k=2
                        )
                        all_results.extend(results)
            else:
                # 通用技术分析搜索
                for data_type in technical_data_types:
                    results = rag_retriever.search_by_data_type(
                        data_type=data_type,
                        query=query,
                        top_k=3
                    )
                    all_results.extend(results)
            
            # 去重并构建上下文
            unique_results = rag_retriever._deduplicate_results(all_results)
            context_text = rag_retriever.build_context_text(unique_results, max_length)
            summary = rag_retriever.get_context_summary(unique_results)
            
            return {
                "context_text": context_text,
                "sources": unique_results,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"构建技术分析上下文失败: {e}")
            return {"context_text": "", "sources": [], "summary": {}}
    
    def _build_fundamental_analysis_context(
        self,
        query: str,
        stock_codes: Optional[List[str]] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        max_length: int = 4000
    ) -> Dict[str, Any]:
        """构建基本面分析上下文"""
        try:
            # 基本面分析相关的数据类型
            fundamental_data_types = ["fundamental", "basic_info", "money_flow"]
            
            all_results = []
            
            if stock_codes:
                # 为指定股票获取基本面数据
                for stock_code in stock_codes:
                    for data_type in fundamental_data_types:
                        results = rag_retriever.search_stock_context(
                            stock_code=stock_code,
                            context_types=[data_type],
                            top_k=3
                        )
                        all_results.extend(results)
            else:
                # 通用基本面分析搜索
                for data_type in fundamental_data_types:
                    results = rag_retriever.search_by_data_type(
                        data_type=data_type,
                        query=query,
                        top_k=5
                    )
                    all_results.extend(results)
            
            # 去重并构建上下文
            unique_results = rag_retriever._deduplicate_results(all_results)
            context_text = rag_retriever.build_context_text(unique_results, max_length)
            summary = rag_retriever.get_context_summary(unique_results)
            
            return {
                "context_text": context_text,
                "sources": unique_results,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"构建基本面分析上下文失败: {e}")
            return {"context_text": "", "sources": [], "summary": {}}
    
    def _build_general_query_context(
        self,
        query: str,
        stock_codes: Optional[List[str]] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
        max_length: int = 4000
    ) -> Dict[str, Any]:
        """构建通用查询上下文"""
        try:
            # 使用混合搜索获取最相关的结果
            results = rag_retriever.hybrid_search(
                query=query,
                stock_codes=stock_codes,
                industries=additional_filters.get("industries") if additional_filters else None,
                data_types=additional_filters.get("data_types") if additional_filters else None,
                top_k=15
            )
            
            context_text = rag_retriever.build_context_text(results, max_length)
            summary = rag_retriever.get_context_summary(results)
            
            return {
                "context_text": context_text,
                "sources": results,
                "summary": summary
            }
            
        except Exception as e:
            logger.error(f"构建通用查询上下文失败: {e}")
            return {"context_text": "", "sources": [], "summary": {}}
    
    def enhance_context_with_realtime_data(
        self,
        context_data: Dict[str, Any],
        stock_codes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """使用实时数据增强上下文"""
        try:
            if not stock_codes:
                # 从上下文摘要中提取股票代码
                summary = context_data.get("summary", {})
                stock_codes = summary.get("stocks", [])
            
            if not stock_codes:
                return context_data
            
            # 获取最新的股票数据
            realtime_parts = []
            for stock_code in stock_codes[:3]:  # 限制最多3只股票
                try:
                    # 获取最新数据
                    latest_data = stock_processor.get_comprehensive_stock_data(stock_code, days=1)
                    if latest_data:
                        formatted_data = stock_processor.format_stock_data_for_analysis(latest_data)
                        if formatted_data:
                            realtime_parts.append(f"=== {stock_code} 最新数据 ===")
                            realtime_parts.append(formatted_data)
                except Exception as e:
                    logger.warning(f"获取 {stock_code} 实时数据失败: {e}")
                    continue
            
            if realtime_parts:
                # 将实时数据添加到上下文前面
                realtime_context = "\n\n".join(realtime_parts)
                original_context = context_data.get("context_text", "")
                
                # 合并上下文，确保不超过长度限制
                max_length = context_data.get("max_length", self.max_context_length)
                if len(realtime_context) + len(original_context) > max_length:
                    # 如果超长，截断原始上下文
                    remaining_length = max_length - len(realtime_context) - 100
                    if remaining_length > 0:
                        original_context = original_context[:remaining_length] + "..."
                
                enhanced_context = f"{realtime_context}\n\n{original_context}"
                context_data["context_text"] = enhanced_context
                context_data["enhanced_with_realtime"] = True
            
            return context_data
            
        except Exception as e:
            logger.error(f"增强上下文失败: {e}")
            return context_data


# 全局上下文构建器实例
context_builder = ContextBuilder()
