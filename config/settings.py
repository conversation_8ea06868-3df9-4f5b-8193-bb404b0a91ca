"""
项目配置管理模块
"""
import os
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings





class Settings(BaseSettings):
    """主配置类"""

    # 环境配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")

    # MySQL配置
    mysql_host: str = Field(default="localhost", env="MYSQL_HOST")
    mysql_port: int = Field(default=3306, env="MYSQL_PORT")
    mysql_user: str = Field(default="root", env="MYSQL_USER")
    mysql_password: str = Field(default="root", env="MYSQL_PASSWORD")
    mysql_database: str = Field(default="stock_cursor", env="MYSQL_DATABASE")
    mysql_charset: str = Field(default="utf8mb4", env="MYSQL_CHARSET")
    mysql_pool_size: int = Field(default=10, env="MYSQL_POOL_SIZE")
    mysql_max_overflow: int = Field(default=20, env="MYSQL_MAX_OVERFLOW")
    mysql_pool_timeout: int = Field(default=30, env="MYSQL_POOL_TIMEOUT")

    # ChromaDB配置
    chroma_persist_directory: str = Field(default="./data/chroma_db", env="CHROMA_PERSIST_DIRECTORY")
    chroma_collection_name: str = Field(default="stock_vectors", env="CHROMA_COLLECTION_NAME")

    # Ollama配置
    ollama_base_url: str = Field(default="http://localhost:11434", env="OLLAMA_BASE_URL")
    ollama_default_model: str = Field(default="qwen2.5:latest", env="OLLAMA_DEFAULT_MODEL")
    ollama_code_model: str = Field(default="qwen2.5-coder:latest", env="OLLAMA_CODE_MODEL")
    ollama_reasoning_model: str = Field(default="deepseek-r1:latest", env="OLLAMA_REASONING_MODEL")
    ollama_embedding_model: str = Field(default="bge-large:latest", env="OLLAMA_EMBEDDING_MODEL")
    ollama_embedding_model_backup: str = Field(default="bge-m3:latest", env="OLLAMA_EMBEDDING_MODEL_BACKUP")
    ollama_temperature: float = Field(default=0.7, env="OLLAMA_TEMPERATURE")
    ollama_max_tokens: int = Field(default=2048, env="OLLAMA_MAX_TOKENS")
    ollama_timeout: int = Field(default=60, env="OLLAMA_TIMEOUT")

    # RAG配置
    rag_top_k: int = Field(default=10, env="RAG_TOP_K")
    rag_similarity_threshold: float = Field(default=0.7, env="RAG_SIMILARITY_THRESHOLD")
    rag_max_context_length: int = Field(default=4000, env="RAG_MAX_CONTEXT_LENGTH")
    rag_chunk_size: int = Field(default=500, env="RAG_CHUNK_SIZE")
    rag_chunk_overlap: int = Field(default=50, env="RAG_CHUNK_OVERLAP")
    rag_enable_cache: bool = Field(default=True, env="RAG_ENABLE_CACHE")
    rag_cache_ttl: int = Field(default=3600, env="RAG_CACHE_TTL")

    # API配置
    api_title: str = Field(default="股票分析RAG系统", env="API_TITLE")
    api_description: str = Field(default="基于RAG+大模型的股票分析系统", env="API_DESCRIPTION")
    api_version: str = Field(default="1.0.0", env="API_VERSION")
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")

    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    log_file_path: str = Field(default="./logs/app.log", env="LOG_FILE_PATH")
    log_file_rotation: str = Field(default="1 day", env="LOG_FILE_ROTATION")
    log_file_retention: str = Field(default="30 days", env="LOG_FILE_RETENTION")
    log_console_enabled: bool = Field(default=True, env="LOG_CONSOLE_ENABLED")

    @property
    def mysql_url(self) -> str:
        """获取MySQL连接URL"""
        return (
            f"mysql+pymysql://{self.mysql_user}:{self.mysql_password}"
            f"@{self.mysql_host}:{self.mysql_port}/{self.mysql_database}"
            f"?charset={self.mysql_charset}"
        )

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False
    }


# 全局配置实例
settings = Settings()
