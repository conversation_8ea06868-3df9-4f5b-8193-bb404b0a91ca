"""
ChromaDB向量数据库连接管理模块
"""
import os
from typing import List, Dict, Any, Optional, Union
import chromadb
from chromadb.config import Settings as ChromaSettings
from chromadb.api.models.Collection import Collection

from config.settings import settings
from utils.logger import get_logger

logger = get_logger("vector_client")


class VectorClient:
    """ChromaDB向量数据库客户端"""
    
    def __init__(self):
        self._client: Optional[chromadb.Client] = None
        self._collection: Optional[Collection] = None
        self._initialized = False
    
    def initialize(self) -> None:
        """初始化向量数据库连接"""
        if self._initialized:
            return
        
        try:
            # 确保持久化目录存在
            persist_dir = settings.chroma_persist_directory
            os.makedirs(persist_dir, exist_ok=True)
            
            # 创建ChromaDB客户端
            self._client = chromadb.PersistentClient(
                path=persist_dir,
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 获取或创建集合
            self._collection = self._get_or_create_collection()
            
            self._initialized = True
            logger.info("ChromaDB向量数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"ChromaDB向量数据库连接初始化失败: {e}")
            raise
    
    def _get_or_create_collection(self) -> Collection:
        """获取或创建集合"""
        collection_name = settings.chroma_collection_name
        
        try:
            # 尝试获取现有集合
            collection = self._client.get_collection(name=collection_name)
            logger.info(f"获取现有集合: {collection_name}")
        except Exception:
            # 创建新集合
            collection = self._client.create_collection(
                name=collection_name,
                metadata={"description": "股票数据向量集合"}
            )
            logger.info(f"创建新集合: {collection_name}")
        
        return collection
    
    def add_documents(
        self,
        documents: List[str],
        metadatas: List[Dict[str, Any]],
        ids: List[str],
        embeddings: Optional[List[List[float]]] = None
    ) -> None:
        """添加文档到向量数据库"""
        if not self._initialized:
            self.initialize()
        
        try:
            self._collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids,
                embeddings=embeddings
            )
            logger.info(f"成功添加 {len(documents)} 个文档到向量数据库")
            
        except Exception as e:
            logger.error(f"添加文档到向量数据库失败: {e}")
            raise
    
    def update_documents(
        self,
        documents: List[str],
        metadatas: List[Dict[str, Any]],
        ids: List[str],
        embeddings: Optional[List[List[float]]] = None
    ) -> None:
        """更新向量数据库中的文档"""
        if not self._initialized:
            self.initialize()
        
        try:
            self._collection.update(
                documents=documents,
                metadatas=metadatas,
                ids=ids,
                embeddings=embeddings
            )
            logger.info(f"成功更新 {len(documents)} 个文档")
            
        except Exception as e:
            logger.error(f"更新文档失败: {e}")
            raise
    
    def query_documents(
        self,
        query_texts: Optional[List[str]] = None,
        query_embeddings: Optional[List[List[float]]] = None,
        n_results: int = 10,
        where: Optional[Dict[str, Any]] = None,
        where_document: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """查询向量数据库"""
        if not self._initialized:
            self.initialize()
        
        try:
            results = self._collection.query(
                query_texts=query_texts,
                query_embeddings=query_embeddings,
                n_results=n_results,
                where=where,
                where_document=where_document
            )
            
            logger.info(f"向量查询完成，返回 {len(results.get('ids', [[]])[0])} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"向量查询失败: {e}")
            raise
    
    def delete_documents(self, ids: List[str]) -> None:
        """删除文档"""
        if not self._initialized:
            self.initialize()
        
        try:
            self._collection.delete(ids=ids)
            logger.info(f"成功删除 {len(ids)} 个文档")
            
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            raise
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        if not self._initialized:
            self.initialize()
        
        try:
            count = self._collection.count()
            return {
                "name": self._collection.name,
                "count": count,
                "metadata": self._collection.metadata
            }
        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            raise
    
    def peek_documents(self, limit: int = 10) -> Dict[str, Any]:
        """查看集合中的文档样本"""
        if not self._initialized:
            self.initialize()
        
        try:
            return self._collection.peek(limit=limit)
        except Exception as e:
            logger.error(f"查看文档样本失败: {e}")
            raise
    
    def reset_collection(self) -> None:
        """重置集合（删除所有数据）"""
        if not self._initialized:
            self.initialize()
        
        try:
            # 删除现有集合
            self._client.delete_collection(
                name=settings.chroma_collection_name
            )
            
            # 重新创建集合
            self._collection = self._get_or_create_collection()
            
            logger.info("集合重置完成")
            
        except Exception as e:
            logger.error(f"重置集合失败: {e}")
            raise
    
    def close(self) -> None:
        """关闭向量数据库连接"""
        if self._client:
            # ChromaDB客户端通常不需要显式关闭
            logger.info("ChromaDB向量数据库连接已关闭")


# 全局向量数据库客户端实例
vector_client = VectorClient()
