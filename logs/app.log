2025-06-29 09:03:57 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:03:57 | INFO | database.vector_client:_get_or_create_collection:67 | 创建新集合: stock_vectors
2025-06-29 09:03:57 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:04:17 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:04:17 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:10:45 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:10:45 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:10:45 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:13:43 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:13:44 | INFO | vectorization.vector_manager:add_stock_to_vector_db:27 | 开始向量化股票数据: 000001.SZ
2025-06-29 09:13:44 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:13:44 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:13:44 | INFO | data_processing.stock_processor:get_comprehensive_stock_data:237 | 获取股票综合数据成功 000001.SZ: {'daily_records': 4, 'technical_records': 4, 'fundamental_records': 1, 'money_flow_records': 4}
2025-06-29 09:13:44 | INFO | vectorization.stock_vectorizer:create_stock_text_chunks:61 | 为股票 000001.SZ(平安银行) 创建了 8 个文本块
2025-06-29 09:13:44 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:13:44 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:13:45 | INFO | vectorization.stock_vectorizer:vectorize_text_chunks:473 | 成功向量化 8 个文本块
2025-06-29 09:13:45 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:13:45 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:13:45 | ERROR | database.vector_client:add_documents:92 | 添加文档到向量数据库失败: Expected metadata value to be a str, int, float or bool, got 2025-06-27 which is a <class 'datetime.date'>
2025-06-29 09:13:45 | ERROR | vectorization.vector_manager:add_stock_to_vector_db:73 | 向量化股票数据失败 000001.SZ: Expected metadata value to be a str, int, float or bool, got 2025-06-27 which is a <class 'datetime.date'>
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:16:19 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:16:20 | INFO | vectorization.vector_manager:add_stock_to_vector_db:27 | 开始向量化股票数据: 000001.SZ
2025-06-29 09:16:20 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:16:20 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:16:20 | INFO | data_processing.stock_processor:get_comprehensive_stock_data:237 | 获取股票综合数据成功 000001.SZ: {'daily_records': 4, 'technical_records': 4, 'fundamental_records': 1, 'money_flow_records': 4}
2025-06-29 09:16:20 | INFO | vectorization.stock_vectorizer:create_stock_text_chunks:82 | 为股票 000001.SZ(平安银行) 创建了 8 个文本块
2025-06-29 09:16:20 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:16:20 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:16:20 | INFO | vectorization.stock_vectorizer:vectorize_text_chunks:501 | 成功向量化 8 个文本块
2025-06-29 09:16:20 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:16:20 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:16:21 | INFO | database.vector_client:add_documents:89 | 成功添加 8 个文档到向量数据库
2025-06-29 09:16:21 | INFO | vectorization.vector_manager:add_stock_to_vector_db:69 | 成功向量化股票数据: 000001.SZ, 创建了 8 个向量
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | vectorization.vector_manager:search_similar_stocks:151 | 向量搜索失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:37 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:16:37 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:16:37 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:16:37 | INFO | database.vector_client:_get_or_create_collection:67 | 创建新集合: stock_vectors
2025-06-29 09:16:37 | INFO | database.vector_client:reset_collection:201 | 集合重置完成
2025-06-29 09:17:01 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:17:02 | INFO | vectorization.vector_manager:add_stock_to_vector_db:27 | 开始向量化股票数据: 000001.SZ
2025-06-29 09:17:02 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:17:02 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:17:02 | INFO | data_processing.stock_processor:get_comprehensive_stock_data:237 | 获取股票综合数据成功 000001.SZ: {'daily_records': 4, 'technical_records': 4, 'fundamental_records': 1, 'money_flow_records': 4}
2025-06-29 09:17:02 | INFO | vectorization.stock_vectorizer:create_stock_text_chunks:82 | 为股票 000001.SZ(平安银行) 创建了 8 个文本块
2025-06-29 09:17:02 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:17:02 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:17:03 | INFO | vectorization.stock_vectorizer:vectorize_text_chunks:501 | 成功向量化 8 个文本块
2025-06-29 09:17:03 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:17:03 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:17:03 | INFO | database.vector_client:add_documents:89 | 成功添加 8 个文档到向量数据库
2025-06-29 09:17:03 | INFO | vectorization.vector_manager:add_stock_to_vector_db:69 | 成功向量化股票数据: 000001.SZ, 创建了 8 个向量
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | vectorization.vector_manager:search_similar_stocks:151 | 向量搜索失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:19 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:17:19 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:17:19 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
