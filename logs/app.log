2025-06-29 09:03:57 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:03:57 | INFO | database.vector_client:_get_or_create_collection:67 | 创建新集合: stock_vectors
2025-06-29 09:03:57 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:04:17 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:04:17 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:10:45 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:10:45 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:10:45 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:13:43 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:13:44 | INFO | vectorization.vector_manager:add_stock_to_vector_db:27 | 开始向量化股票数据: 000001.SZ
2025-06-29 09:13:44 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:13:44 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:13:44 | INFO | data_processing.stock_processor:get_comprehensive_stock_data:237 | 获取股票综合数据成功 000001.SZ: {'daily_records': 4, 'technical_records': 4, 'fundamental_records': 1, 'money_flow_records': 4}
2025-06-29 09:13:44 | INFO | vectorization.stock_vectorizer:create_stock_text_chunks:61 | 为股票 000001.SZ(平安银行) 创建了 8 个文本块
2025-06-29 09:13:44 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:13:44 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:13:44 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:13:45 | INFO | vectorization.stock_vectorizer:vectorize_text_chunks:473 | 成功向量化 8 个文本块
2025-06-29 09:13:45 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:13:45 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:13:45 | ERROR | database.vector_client:add_documents:92 | 添加文档到向量数据库失败: Expected metadata value to be a str, int, float or bool, got 2025-06-27 which is a <class 'datetime.date'>
2025-06-29 09:13:45 | ERROR | vectorization.vector_manager:add_stock_to_vector_db:73 | 向量化股票数据失败 000001.SZ: Expected metadata value to be a str, int, float or bool, got 2025-06-27 which is a <class 'datetime.date'>
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:48 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:13:49 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:16:19 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:16:20 | INFO | vectorization.vector_manager:add_stock_to_vector_db:27 | 开始向量化股票数据: 000001.SZ
2025-06-29 09:16:20 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:16:20 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:16:20 | INFO | data_processing.stock_processor:get_comprehensive_stock_data:237 | 获取股票综合数据成功 000001.SZ: {'daily_records': 4, 'technical_records': 4, 'fundamental_records': 1, 'money_flow_records': 4}
2025-06-29 09:16:20 | INFO | vectorization.stock_vectorizer:create_stock_text_chunks:82 | 为股票 000001.SZ(平安银行) 创建了 8 个文本块
2025-06-29 09:16:20 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:16:20 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:16:20 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:16:20 | INFO | vectorization.stock_vectorizer:vectorize_text_chunks:501 | 成功向量化 8 个文本块
2025-06-29 09:16:20 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:16:20 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:16:21 | INFO | database.vector_client:add_documents:89 | 成功添加 8 个文档到向量数据库
2025-06-29 09:16:21 | INFO | vectorization.vector_manager:add_stock_to_vector_db:69 | 成功向量化股票数据: 000001.SZ, 创建了 8 个向量
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | vectorization.vector_manager:search_similar_stocks:151 | 向量搜索失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:22 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:23 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:16:37 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:16:37 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:16:37 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:16:37 | INFO | database.vector_client:_get_or_create_collection:67 | 创建新集合: stock_vectors
2025-06-29 09:16:37 | INFO | database.vector_client:reset_collection:201 | 集合重置完成
2025-06-29 09:17:01 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:17:02 | INFO | vectorization.vector_manager:add_stock_to_vector_db:27 | 开始向量化股票数据: 000001.SZ
2025-06-29 09:17:02 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:17:02 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:17:02 | INFO | data_processing.stock_processor:get_comprehensive_stock_data:237 | 获取股票综合数据成功 000001.SZ: {'daily_records': 4, 'technical_records': 4, 'fundamental_records': 1, 'money_flow_records': 4}
2025-06-29 09:17:02 | INFO | vectorization.stock_vectorizer:create_stock_text_chunks:82 | 为股票 000001.SZ(平安银行) 创建了 8 个文本块
2025-06-29 09:17:02 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:17:02 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:17:02 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:17:03 | INFO | vectorization.stock_vectorizer:vectorize_text_chunks:501 | 成功向量化 8 个文本块
2025-06-29 09:17:03 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:17:03 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:17:03 | INFO | database.vector_client:add_documents:89 | 成功添加 8 个文档到向量数据库
2025-06-29 09:17:03 | INFO | vectorization.vector_manager:add_stock_to_vector_db:69 | 成功向量化股票数据: 000001.SZ, 创建了 8 个向量
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | vectorization.vector_manager:search_similar_stocks:151 | 向量搜索失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:04 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:05 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Embedding dimension 384 does not match collection dimensionality 1024
2025-06-29 09:17:19 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:17:19 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:17:19 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:17:19 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:22:20 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:22:41 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '平安银行股票分析', top_k: 5
2025-06-29 09:22:42 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:22:42 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:22:42 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:22:42 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:22:42 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:22:42 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:22:42 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:22:42 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:22:42 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 5 个结果
2025-06-29 09:22:42 | INFO | rag.retriever:search_relevant_context:75 | RAG检索完成，返回 0 个相关结果
2025-06-29 09:22:42 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 3
2025-06-29 09:22:42 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 3 个结果
2025-06-29 09:22:42 | INFO | rag.retriever:search_relevant_context:75 | RAG检索完成，返回 0 个相关结果
2025-06-29 09:22:42 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:42 | INFO | rag.context_builder:build_context:40 | 开始构建上下文，类型: stock_analysis, 查询: '分析平安银行的投资价值'
2025-06-29 09:22:42 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:42 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'basic_info'}
2025-06-29 09:22:42 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'basic_info'}
2025-06-29 09:22:42 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:42 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:42 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'price_data'}
2025-06-29 09:22:42 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'price_data'}
2025-06-29 09:22:42 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:42 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:42 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_macd'}
2025-06-29 09:22:42 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_macd'}
2025-06-29 09:22:42 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:42 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_kdj'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_kdj'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_rsi'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_rsi'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'fundamental'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'fundamental'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'money_flow'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'money_flow'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:build_context_text:326 | 构建上下文文本完成，长度: 0
2025-06-29 09:22:43 | INFO | rag.context_builder:build_context:64 | 上下文构建完成，类型: stock_analysis
2025-06-29 09:22:43 | INFO | rag.context_builder:build_context:40 | 开始构建上下文，类型: technical_analysis, 查询: '技术指标分析'
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_macd'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_macd'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_kdj'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_kdj'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_rsi'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_rsi'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_boll'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_boll'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'price_data'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'price_data'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:43 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'trend_analysis'}
2025-06-29 09:22:43 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'trend_analysis'}
2025-06-29 09:22:43 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:43 | INFO | rag.retriever:build_context_text:326 | 构建上下文文本完成，长度: 0
2025-06-29 09:22:43 | INFO | rag.context_builder:build_context:64 | 上下文构建完成，类型: technical_analysis
2025-06-29 09:22:43 | INFO | analysis.stock_analyzer:analyze_stock:37 | 开始分析股票 000001.SZ，分析类型: comprehensive
2025-06-29 09:22:44 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:22:44 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:22:44 | INFO | rag.context_builder:build_context:40 | 开始构建上下文，类型: stock_analysis, 查询: '分析股票 000001.SZ 平安银行'
2025-06-29 09:22:44 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:44 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'basic_info'}
2025-06-29 09:22:44 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'basic_info'}
2025-06-29 09:22:44 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:44 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:44 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'price_data'}
2025-06-29 09:22:44 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'price_data'}
2025-06-29 09:22:44 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:44 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:44 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_macd'}
2025-06-29 09:22:44 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_macd'}
2025-06-29 09:22:44 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:44 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:44 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_kdj'}
2025-06-29 09:22:44 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_kdj'}
2025-06-29 09:22:44 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:44 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:44 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_rsi'}
2025-06-29 09:22:44 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'technical_rsi'}
2025-06-29 09:22:44 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:44 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:44 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'fundamental'}
2025-06-29 09:22:44 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'fundamental'}
2025-06-29 09:22:44 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:44 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:22:44 | ERROR | database.vector_client:query_documents:144 | 向量查询失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'money_flow'}
2025-06-29 09:22:44 | ERROR | rag.retriever:search_relevant_context:79 | RAG检索失败: Expected where to have exactly one operator, got {'ts_code': '000001.SZ', 'data_type': 'money_flow'}
2025-06-29 09:22:44 | INFO | rag.retriever:search_stock_context:112 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:22:44 | INFO | rag.retriever:build_context_text:326 | 构建上下文文本完成，长度: 0
2025-06-29 09:22:44 | INFO | rag.context_builder:build_context:64 | 上下文构建完成，类型: stock_analysis
2025-06-29 09:22:44 | INFO | data_processing.stock_processor:get_comprehensive_stock_data:237 | 获取股票综合数据成功 000001.SZ: {'daily_records': 0, 'technical_records': 0, 'fundamental_records': 0, 'money_flow_records': 0}
2025-06-29 09:25:16 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:25:17 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '平安银行', top_k: 5
2025-06-29 09:25:17 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:25:17 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:25:17 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:25:17 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:25:17 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:25:17 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:25:17 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:25:17 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:25:17 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 5 个结果
2025-06-29 09:25:17 | INFO | rag.retriever:search_relevant_context:86 | RAG检索完成，返回 0 个相关结果
2025-06-29 09:25:17 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票', top_k: 3
2025-06-29 09:25:17 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 3 个结果
2025-06-29 09:25:17 | INFO | rag.retriever:search_relevant_context:86 | RAG检索完成，返回 0 个相关结果
2025-06-29 09:25:41 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:25:41 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:25:41 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:25:41 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:25:41 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:25:41 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:25:41 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:25:41 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:25:41 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:25:42 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 5 个结果
2025-06-29 09:26:05 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:26:05 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:26:05 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:26:05 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:26:05 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:26:05 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:26:05 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:26:05 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:26:05 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:26:05 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 5 个结果
2025-06-29 09:26:27 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:26:27 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:26:27 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:26:27 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:26:27 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:26:27 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:26:27 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:26:28 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:26:28 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:26:28 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 5 个结果
2025-06-29 09:26:36 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:26:38 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '平安银行', top_k: 5
2025-06-29 09:26:38 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:26:38 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:26:38 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:26:38 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:26:38 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:26:38 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:26:38 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:26:38 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:26:38 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 5 个结果
2025-06-29 09:26:38 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 5 个相关结果
2025-06-29 09:26:38 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票', top_k: 3
2025-06-29 09:26:38 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 3 个结果
2025-06-29 09:26:38 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 3 个相关结果
2025-06-29 09:27:01 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:27:02 | INFO | analysis.stock_analyzer:analyze_stock:37 | 开始分析股票 000001.SZ，分析类型: comprehensive
2025-06-29 09:27:02 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:27:02 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:27:02 | INFO | rag.context_builder:build_context:40 | 开始构建上下文，类型: stock_analysis, 查询: '分析股票 000001.SZ 平安银行'
2025-06-29 09:27:02 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:27:02 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:27:02 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:27:02 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:27:02 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:27:02 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:27:02 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:27:03 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:27:03 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:27:03 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:27:03 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:27:03 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:27:03 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:27:03 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:27:03 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:27:03 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:27:03 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:27:03 | INFO | rag.retriever:build_context_text:350 | 构建上下文文本完成，长度: 799
2025-06-29 09:27:03 | INFO | rag.context_builder:build_context:64 | 上下文构建完成，类型: stock_analysis
2025-06-29 09:28:03 | ERROR | llm.ollama_client:_make_request:56 | Ollama请求超时: http://localhost:11434/api/chat
2025-06-29 09:28:03 | ERROR | llm.ollama_client:chat_completion:177 | 聊天对话失败: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=60)
2025-06-29 09:28:03 | ERROR | llm.model_manager:chat_completion:176 | 聊天对话失败: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=60)
2025-06-29 09:28:03 | ERROR | analysis.stock_analyzer:analyze_stock:119 | 股票分析失败 000001.SZ: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=60)
2025-06-29 09:28:03 | INFO | analysis.stock_analyzer:analyze_stock:37 | 开始分析股票 000001.SZ，分析类型: technical
2025-06-29 09:28:03 | INFO | rag.context_builder:build_context:40 | 开始构建上下文，类型: technical_analysis, 查询: '分析股票 000001.SZ 平安银行'
2025-06-29 09:28:03 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:04 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:04 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:04 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:04 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:04 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:04 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 0 个相关结果
2025-06-29 09:28:04 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:28:04 | INFO | rag.retriever:build_context_text:350 | 构建上下文文本完成，长度: 550
2025-06-29 09:28:04 | INFO | rag.context_builder:build_context:64 | 上下文构建完成，类型: technical_analysis
2025-06-29 09:28:51 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:28:52 | INFO | analysis.stock_analyzer:analyze_stock:37 | 开始分析股票 000001.SZ，分析类型: comprehensive
2025-06-29 09:28:53 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:28:53 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:28:53 | INFO | rag.context_builder:build_context:40 | 开始构建上下文，类型: stock_analysis, 查询: '分析股票 000001.SZ 平安银行'
2025-06-29 09:28:53 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:53 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:28:53 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:28:53 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:28:53 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:28:53 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:28:53 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:28:53 | INFO | database.vector_client:_get_or_create_collection:60 | 获取现有集合: stock_vectors
2025-06-29 09:28:53 | INFO | database.vector_client:initialize:47 | ChromaDB向量数据库连接初始化成功
2025-06-29 09:28:53 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:53 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:53 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:53 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:53 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:53 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:53 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:53 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:54 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:54 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:54 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:54 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:28:54 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:28:54 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:28:54 | INFO | rag.retriever:build_context_text:350 | 构建上下文文本完成，长度: 799
2025-06-29 09:28:54 | INFO | rag.context_builder:build_context:64 | 上下文构建完成，类型: stock_analysis
2025-06-29 09:29:54 | ERROR | llm.ollama_client:_make_request:56 | Ollama请求超时: http://localhost:11434/api/chat
2025-06-29 09:29:54 | ERROR | llm.ollama_client:chat_completion:177 | 聊天对话失败: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=60)
2025-06-29 09:29:54 | ERROR | llm.model_manager:chat_completion:176 | 聊天对话失败: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=60)
2025-06-29 09:29:54 | ERROR | analysis.stock_analyzer:analyze_stock:119 | 股票分析失败 000001.SZ: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=60)
2025-06-29 09:29:54 | INFO | analysis.stock_analyzer:analyze_stock:37 | 开始分析股票 000001.SZ，分析类型: technical
2025-06-29 09:29:54 | INFO | rag.context_builder:build_context:40 | 开始构建上下文，类型: technical_analysis, 查询: '分析股票 000001.SZ 平安银行'
2025-06-29 09:29:54 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:29:55 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:29:55 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:29:55 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:29:55 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:29:55 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 1 个相关结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 1 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:34 | 开始RAG检索，查询: '股票代码 000001.SZ', top_k: 2
2025-06-29 09:29:55 | INFO | database.vector_client:query_documents:140 | 向量查询完成，返回 0 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_relevant_context:94 | RAG检索完成，返回 0 个相关结果
2025-06-29 09:29:55 | INFO | rag.retriever:search_stock_context:136 | 股票 000001.SZ 上下文检索完成，返回 0 个结果
2025-06-29 09:29:55 | INFO | rag.retriever:build_context_text:350 | 构建上下文文本完成，长度: 550
2025-06-29 09:29:55 | INFO | rag.context_builder:build_context:64 | 上下文构建完成，类型: technical_analysis
2025-06-29 09:40:36 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:40:37 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:40:37 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:40:38 | INFO | text2sql.sql_generator:generate_sql:46 | 开始生成SQL，查询: '查询平安银行的基本信息'
2025-06-29 09:40:38 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:40:38 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:40:38 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:40:38 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:40:38 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:40:38 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:41:07 | INFO | text2sql.query_validator:validate_sql:70 | SQL验证完成，有效性: True
2025-06-29 09:41:07 | INFO | text2sql.sql_generator:generate_sql:103 | SQL生成成功: SELECT * FROM stock_top10_holders WHERE ts_code = '000001.SZ' LIMIT 1;...
2025-06-29 09:41:07 | INFO | text2sql.sql_generator:generate_sql:46 | 开始生成SQL，查询: '查询最近30天涨幅前10的股票'
2025-06-29 09:41:37 | INFO | text2sql.query_validator:validate_sql:70 | SQL验证完成，有效性: True
2025-06-29 09:41:37 | INFO | text2sql.sql_generator:generate_sql:103 | SQL生成成功: SELECT 
    s.ts_code, 
    s.name, 
    s.pct_change
FROM 
    stock_daily_history s
JOIN 
    stoc...
2025-06-29 09:41:37 | INFO | text2sql.text2sql_engine:explain_query:98 | 解释查询: '查询平安银行的股票代码和名称'
2025-06-29 09:41:37 | INFO | text2sql.sql_generator:generate_sql:46 | 开始生成SQL，查询: '查询平安银行的股票代码和名称'
2025-06-29 09:43:01 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:43:01 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:43:01 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:43:33 | INFO | utils.logger:setup_logger:59 | 日志系统初始化完成
2025-06-29 09:43:34 | INFO | text2sql.sql_generator:generate_sql:46 | 开始生成SQL，查询: '查询前5个股票的基本信息'
2025-06-29 09:43:34 | INFO | database.mysql_client:test_connection:65 | MySQL数据库连接测试成功
2025-06-29 09:43:34 | INFO | database.mysql_client:initialize:53 | MySQL数据库连接初始化成功
2025-06-29 09:43:34 | INFO | llm.ollama_client:get_available_models:88 | 获取到 8 个可用模型
2025-06-29 09:43:34 | INFO | llm.model_manager:_check_all_models:100 | text_generation - 主要模型: qwen2.5:latest (可用), 可用备用模型: 1
2025-06-29 09:43:34 | INFO | llm.model_manager:_check_all_models:100 | code_generation - 主要模型: qwen2.5-coder:latest (可用), 可用备用模型: 1
2025-06-29 09:43:34 | INFO | llm.model_manager:_check_all_models:100 | reasoning - 主要模型: deepseek-r1:latest (可用), 可用备用模型: 1
2025-06-29 09:43:34 | INFO | llm.model_manager:_check_all_models:100 | embedding - 主要模型: bge-large:latest (可用), 可用备用模型: 2
2025-06-29 09:43:34 | INFO | llm.model_manager:initialize:62 | 模型管理器初始化完成
2025-06-29 09:43:59 | INFO | text2sql.query_validator:validate_sql:70 | SQL验证完成，有效性: True
2025-06-29 09:43:59 | INFO | text2sql.sql_generator:generate_sql:103 | SQL生成成功: SELECT sb.ts_code, sb.name, sb.sector, sb.industry, sb.list_date 
FROM stock_basic sb 
ORDER BY sb.t...
2025-06-29 09:43:59 | INFO | text2sql.query_validator:validate_sql:70 | SQL验证完成，有效性: True
2025-06-29 09:43:59 | INFO | text2sql.query_executor:execute_query:36 | 开始执行查询: SELECT sb.ts_code, sb.name, sb.sector, sb.industry, sb.list_date 
FROM stock_basic sb 
ORDER BY sb.t...
2025-06-29 09:43:59 | INFO | text2sql.query_validator:validate_sql:70 | SQL验证完成，有效性: True
2025-06-29 09:43:59 | ERROR | database.mysql_client:execute_query:107 | 查询执行失败: SELECT sb.ts_code, sb.name, sb.sector, sb.industry, sb.list_date 
FROM stock_basic sb 
ORDER BY sb.ts_code ASC 
LIMIT 5;, 错误: (pymysql.err.OperationalError) (1054, "Unknown column 'sb.sector' in 'field list'")
[SQL: SELECT sb.ts_code, sb.name, sb.sector, sb.industry, sb.list_date 
FROM stock_basic sb 
ORDER BY sb.ts_code ASC 
LIMIT 5;]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-29 09:43:59 | ERROR | text2sql.query_executor:_execute_sql_safely:303 | SQL执行失败: (pymysql.err.OperationalError) (1054, "Unknown column 'sb.sector' in 'field list'")
[SQL: SELECT sb.ts_code, sb.name, sb.sector, sb.industry, sb.list_date 
FROM stock_basic sb 
ORDER BY sb.ts_code ASC 
LIMIT 5;]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-29 09:43:59 | INFO | text2sql.sql_generator:generate_sql:46 | 开始生成SQL，查询: '查询股票基本信息'
2025-06-29 09:44:26 | INFO | text2sql.query_validator:validate_sql:70 | SQL验证完成，有效性: True
2025-06-29 09:44:26 | INFO | text2sql.sql_generator:generate_sql:103 | SQL生成成功: SELECT sb.ts_code, sb.name, sb.sector, sb.industry, sd.close_price, sd.open_price, sd.high_price, sd...
2025-06-29 09:44:26 | INFO | text2sql.sql_generator:generate_sql:46 | 开始生成SQL，查询: '查询银行股票'
2025-06-29 09:44:49 | INFO | text2sql.query_validator:validate_sql:70 | SQL验证完成，有效性: True
2025-06-29 09:44:49 | INFO | text2sql.sql_generator:generate_sql:103 | SQL生成成功: SELECT sb.ts_code, sb.name 
FROM stock_basic sb 
JOIN stock_daily_history sd ON sb.ts_code = sd.ts_c...
2025-06-29 09:44:49 | INFO | text2sql.sql_generator:generate_sql:46 | 开始生成SQL，查询: '查询前10个股票'
