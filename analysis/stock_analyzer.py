"""
股票分析引擎模块
"""
from typing import Dict, List, Any, Optional
from datetime import datetime, date
import json

from rag.context_builder import context_builder
from llm.model_manager import model_manager, ModelType
from data_processing.stock_processor import stock_processor
from utils.logger import get_logger

logger = get_logger("stock_analyzer")


class StockAnalyzer:
    """股票分析引擎"""
    
    def __init__(self):
        self.analysis_templates = {
            "comprehensive": self._comprehensive_analysis_prompt,
            "technical": self._technical_analysis_prompt,
            "fundamental": self._fundamental_analysis_prompt,
            "risk": self._risk_analysis_prompt,
            "comparison": self._comparison_analysis_prompt
        }
    
    def analyze_stock(
        self,
        stock_code: str,
        analysis_type: str = "comprehensive",
        include_realtime: bool = True,
        custom_query: Optional[str] = None
    ) -> Dict[str, Any]:
        """分析单只股票"""
        try:
            logger.info(f"开始分析股票 {stock_code}，分析类型: {analysis_type}")
            
            # 获取股票基础信息
            basic_info = stock_processor.get_stock_basic_info(stock_code)
            if not basic_info:
                return {
                    "success": False,
                    "error": f"未找到股票 {stock_code} 的基础信息",
                    "stock_code": stock_code
                }
            
            stock_name = basic_info.get("name", "")
            
            # 构建查询
            if custom_query:
                query = custom_query
            else:
                query = f"分析股票 {stock_code} {stock_name}"
            
            # 确定上下文类型
            context_type_map = {
                "comprehensive": "stock_analysis",
                "technical": "technical_analysis", 
                "fundamental": "fundamental_analysis",
                "risk": "stock_analysis",
                "comparison": "stock_analysis"
            }
            context_type = context_type_map.get(analysis_type, "stock_analysis")
            
            # 构建上下文
            context_data = context_builder.build_context(
                query=query,
                context_type=context_type,
                stock_codes=[stock_code]
            )
            
            # 增强实时数据
            if include_realtime:
                context_data = context_builder.enhance_context_with_realtime_data(
                    context_data, [stock_code]
                )
            
            # 生成分析提示词
            prompt_func = self.analysis_templates.get(
                analysis_type, 
                self._comprehensive_analysis_prompt
            )
            
            system_prompt, user_prompt = prompt_func(
                stock_code=stock_code,
                stock_name=stock_name,
                context_data=context_data,
                custom_query=custom_query
            )
            
            # 调用大模型进行分析 - 使用更快的文本生成模型
            analysis_result = model_manager.chat_completion(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                model_type=ModelType.TEXT_GENERATION,  # 使用更快的模型
                temperature=0.3
            )
            
            # 构建返回结果
            result = {
                "success": True,
                "stock_code": stock_code,
                "stock_name": stock_name,
                "analysis_type": analysis_type,
                "analysis_result": analysis_result,
                "context_summary": context_data.get("summary", {}),
                "sources_count": len(context_data.get("sources", [])),
                "generated_at": datetime.now().isoformat(),
                "include_realtime": include_realtime
            }
            
            logger.info(f"股票 {stock_code} 分析完成")
            return result
            
        except Exception as e:
            logger.error(f"股票分析失败 {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_code": stock_code,
                "analysis_type": analysis_type
            }
    
    def compare_stocks(
        self,
        stock_codes: List[str],
        comparison_aspects: Optional[List[str]] = None,
        include_realtime: bool = True
    ) -> Dict[str, Any]:
        """比较多只股票"""
        try:
            logger.info(f"开始比较股票: {stock_codes}")
            
            if len(stock_codes) < 2:
                return {
                    "success": False,
                    "error": "至少需要2只股票进行比较"
                }
            
            # 获取股票基础信息
            stocks_info = {}
            for stock_code in stock_codes:
                basic_info = stock_processor.get_stock_basic_info(stock_code)
                if basic_info:
                    stocks_info[stock_code] = basic_info
            
            if len(stocks_info) < 2:
                return {
                    "success": False,
                    "error": "至少需要2只有效股票进行比较"
                }
            
            # 构建比较查询
            stock_names = [info.get("name", "") for info in stocks_info.values()]
            query = f"比较股票 {', '.join(f'{code}({name})' for code, name in zip(stock_codes, stock_names))}"
            
            # 构建上下文
            context_data = context_builder.build_context(
                query=query,
                context_type="stock_analysis",
                stock_codes=list(stocks_info.keys())
            )
            
            # 增强实时数据
            if include_realtime:
                context_data = context_builder.enhance_context_with_realtime_data(
                    context_data, list(stocks_info.keys())
                )
            
            # 生成比较分析提示词
            system_prompt, user_prompt = self._comparison_analysis_prompt(
                stock_codes=list(stocks_info.keys()),
                stocks_info=stocks_info,
                context_data=context_data,
                comparison_aspects=comparison_aspects
            )
            
            # 调用大模型进行分析
            analysis_result = model_manager.chat_completion(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                model_type=ModelType.TEXT_GENERATION,
                temperature=0.3
            )
            
            # 构建返回结果
            result = {
                "success": True,
                "stock_codes": list(stocks_info.keys()),
                "stocks_info": stocks_info,
                "analysis_type": "comparison",
                "analysis_result": analysis_result,
                "context_summary": context_data.get("summary", {}),
                "sources_count": len(context_data.get("sources", [])),
                "comparison_aspects": comparison_aspects,
                "generated_at": datetime.now().isoformat(),
                "include_realtime": include_realtime
            }
            
            logger.info(f"股票比较分析完成: {stock_codes}")
            return result
            
        except Exception as e:
            logger.error(f"股票比较分析失败 {stock_codes}: {e}")
            return {
                "success": False,
                "error": str(e),
                "stock_codes": stock_codes,
                "analysis_type": "comparison"
            }
    
    def analyze_industry(
        self,
        industry: str,
        analysis_focus: Optional[str] = None,
        top_stocks: int = 5
    ) -> Dict[str, Any]:
        """分析行业"""
        try:
            logger.info(f"开始分析行业: {industry}")
            
            # 构建行业分析查询
            query = f"分析{industry}行业"
            if analysis_focus:
                query += f"的{analysis_focus}"
            
            # 构建上下文
            context_data = context_builder.build_context(
                query=query,
                context_type="industry_comparison",
                additional_filters={"industries": [industry]}
            )
            
            # 生成行业分析提示词
            system_prompt = f"""
你是一个专业的行业分析师。请基于提供的数据对{industry}行业进行深入分析。

分析要求：
1. 行业整体概况和发展趋势
2. 主要上市公司表现分析
3. 行业估值水平和投资价值
4. 风险因素和机会分析
5. 投资建议和关注重点

请提供客观、专业的分析，避免给出具体的投资建议。
"""
            
            user_prompt = f"""
请分析{industry}行业，重点关注：{analysis_focus or '整体发展状况'}

相关数据：
{context_data.get('context_text', '')}

请提供详细的行业分析报告。
"""
            
            # 调用大模型进行分析
            analysis_result = model_manager.chat_completion(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                model_type=ModelType.TEXT_GENERATION,
                temperature=0.3
            )
            
            # 构建返回结果
            result = {
                "success": True,
                "industry": industry,
                "analysis_type": "industry",
                "analysis_focus": analysis_focus,
                "analysis_result": analysis_result,
                "context_summary": context_data.get("summary", {}),
                "sources_count": len(context_data.get("sources", [])),
                "generated_at": datetime.now().isoformat()
            }
            
            logger.info(f"行业 {industry} 分析完成")
            return result
            
        except Exception as e:
            logger.error(f"行业分析失败 {industry}: {e}")
            return {
                "success": False,
                "error": str(e),
                "industry": industry,
                "analysis_type": "industry"
            }
    
    def _comprehensive_analysis_prompt(
        self,
        stock_code: str,
        stock_name: str,
        context_data: Dict[str, Any],
        custom_query: Optional[str] = None
    ) -> tuple:
        """综合分析提示词"""
        system_prompt = f"""
你是一个专业的股票分析师。请基于提供的数据对股票 {stock_code}({stock_name}) 进行全面的投资分析。

分析框架：
1. 公司基本面分析（业务模式、财务状况、估值水平）
2. 技术面分析（价格趋势、技术指标、支撑阻力）
3. 资金面分析（资金流向、机构持仓、市场情绪）
4. 风险评估（系统性风险、个股风险、流动性风险）
5. 投资建议（目标价位、持有期建议、风险提示）

注意事项：
- 基于客观数据进行分析，避免主观臆测
- 提供平衡的观点，既要看到机会也要识别风险
- 不提供具体的买卖建议，仅供参考
- 使用中文回答，语言专业但易懂
"""
        
        user_prompt = f"""
请对股票 {stock_code}({stock_name}) 进行综合分析。

{f"特别关注：{custom_query}" if custom_query else ""}

相关数据：
{context_data.get('context_text', '')}

请提供详细的投资分析报告。
"""
        
        return system_prompt, user_prompt
    
    def _technical_analysis_prompt(
        self,
        stock_code: str,
        stock_name: str,
        context_data: Dict[str, Any],
        custom_query: Optional[str] = None
    ) -> tuple:
        """技术分析提示词"""
        system_prompt = f"""
你是一个专业的技术分析师。请基于提供的技术指标数据对股票 {stock_code}({stock_name}) 进行技术分析。

技术分析要点：
1. 价格趋势分析（短期、中期、长期趋势）
2. 技术指标解读（MACD、KDJ、RSI、布林带等）
3. 支撑阻力位分析
4. 成交量分析
5. 技术形态识别
6. 买卖信号判断

请提供客观的技术分析，不做具体的操作建议。
"""
        
        user_prompt = f"""
请对股票 {stock_code}({stock_name}) 进行技术分析。

{f"重点分析：{custom_query}" if custom_query else ""}

技术数据：
{context_data.get('context_text', '')}

请提供详细的技术分析报告。
"""
        
        return system_prompt, user_prompt
    
    def _fundamental_analysis_prompt(
        self,
        stock_code: str,
        stock_name: str,
        context_data: Dict[str, Any],
        custom_query: Optional[str] = None
    ) -> tuple:
        """基本面分析提示词"""
        system_prompt = f"""
你是一个专业的基本面分析师。请基于提供的财务和基本面数据对股票 {stock_code}({stock_name}) 进行基本面分析。

基本面分析要点：
1. 估值分析（PE、PB、PS等估值指标）
2. 财务健康度（资产负债、现金流、盈利能力）
3. 成长性分析（收入增长、利润增长、市场份额）
4. 行业地位（竞争优势、市场地位、护城河）
5. 投资价值评估

请提供客观的基本面分析，重点关注长期投资价值。
"""
        
        user_prompt = f"""
请对股票 {stock_code}({stock_name}) 进行基本面分析。

{f"重点关注：{custom_query}" if custom_query else ""}

基本面数据：
{context_data.get('context_text', '')}

请提供详细的基本面分析报告。
"""
        
        return system_prompt, user_prompt
    
    def _risk_analysis_prompt(
        self,
        stock_code: str,
        stock_name: str,
        context_data: Dict[str, Any],
        custom_query: Optional[str] = None
    ) -> tuple:
        """风险分析提示词"""
        system_prompt = f"""
你是一个专业的风险分析师。请基于提供的数据对股票 {stock_code}({stock_name}) 进行全面的风险评估。

风险分析维度：
1. 市场风险（系统性风险、行业风险、个股风险）
2. 财务风险（流动性风险、偿债风险、盈利风险）
3. 经营风险（业务模式风险、竞争风险、管理风险）
4. 流动性风险（交易活跃度、市值规模）
5. 政策风险（监管变化、政策影响）

请提供客观的风险评估，帮助投资者识别和管理风险。
"""
        
        user_prompt = f"""
请对股票 {stock_code}({stock_name}) 进行风险分析。

{f"特别关注：{custom_query}" if custom_query else ""}

相关数据：
{context_data.get('context_text', '')}

请提供详细的风险分析报告。
"""
        
        return system_prompt, user_prompt
    
    def _comparison_analysis_prompt(
        self,
        stock_codes: List[str],
        stocks_info: Dict[str, Any],
        context_data: Dict[str, Any],
        comparison_aspects: Optional[List[str]] = None
    ) -> tuple:
        """比较分析提示词"""
        stocks_desc = ", ".join([
            f"{code}({info.get('name', '')})" 
            for code, info in stocks_info.items()
        ])
        
        aspects = comparison_aspects or [
            "基本面对比", "技术面对比", "估值对比", "风险对比", "投资价值对比"
        ]
        
        system_prompt = f"""
你是一个专业的股票分析师。请对以下股票进行横向比较分析：{stocks_desc}

比较分析框架：
{chr(10).join([f"{i+1}. {aspect}" for i, aspect in enumerate(aspects)])}

分析要求：
- 客观比较各股票的优劣势
- 识别投资机会和风险差异
- 提供相对投资价值评估
- 不提供具体买卖建议
"""
        
        user_prompt = f"""
请比较分析以下股票：{stocks_desc}

比较重点：{', '.join(aspects)}

相关数据：
{context_data.get('context_text', '')}

请提供详细的比较分析报告。
"""
        
        return system_prompt, user_prompt


# 全局股票分析器实例
stock_analyzer = StockAnalyzer()
